import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { logout, selectIsAuthenticated, selectAuthInitialized } from '../redux-store/slices/customerSlice';

const useTokenValidation = () => {
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authInitialized = useSelector(selectAuthInitialized);

  useEffect(() => {
    if (!authInitialized) return;

    const validateToken = () => {
      const token = localStorage.getItem("customerAuthToken");
      const customerData = localStorage.getItem("customer");
      
      if (isAuthenticated && (!token || !customerData)) {
        // Token was removed or expired, logout user
        dispatch(logout());
      }
    };

    // Check token validity on mount and periodically
    validateToken();
    
    // Set up periodic token validation (every 30 seconds)
    const interval = setInterval(validateToken, 30000);
    
    // Listen for storage changes (when token is removed in another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'customerAuthToken' && !e.newValue && isAuthenticated) {
        dispatch(logout());
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [dispatch, isAuthenticated, authInitialized]);
};

export default useTokenValidation;