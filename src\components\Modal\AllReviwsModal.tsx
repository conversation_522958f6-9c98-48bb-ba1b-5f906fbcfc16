import { useState } from 'react';
import { X, ChevronLeft, ChevronRight, Plus, ThumbsUp } from 'lucide-react';
import { assets } from "../../assets/assets";

// Types for our components
interface AllReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  restaurantName: string;
}

interface ReviewCardProps {
  reviewer: string;
  rating: number;
  timeAgo: string;
  reviewText: string;
  likedDishes?: {
    name: string;
    price: number;
    image: string;
  }[];
}

interface RatingBarProps {
  rating: number;
  count: number;
  maxCount: number;
}

const RatingBar = ({ rating, count, maxCount }: RatingBarProps) => {
  const percentage = (count / maxCount) * 100;

  return (
    <div className="flex items-center mb-2">
      <span className="w-6 text-right mr-2">{rating}★</span>
      <div className="flex-grow bg-gray-200 h-2 rounded-full overflow-hidden">
        <div
          className="h-full bg-yellow-400 rounded-full"
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

const ReviewCard = ({ reviewer, rating, timeAgo, reviewText, likedDishes }: ReviewCardProps) => {
  return (
    <div className="border-b border-gray-200 pb-4">
      <div className="font-medium text-lg mb-1">{reviewer}</div>
      <div className="flex items-center mb-2">
        {[...Array(5)].map((_, i) => (
          <span key={i} className={`text-lg ${i < rating ? "text-yellow-400" : "text-gray-300"}`}>★</span>
        ))}
        <span className="text-gray-500 text-sm ml-2">{timeAgo}</span>
      </div>
      <p className="text-gray-700 mb-4">{reviewText}</p>

      {likedDishes && likedDishes.length > 0 && (
        <>
          <div className="text-gray-700 mb-2">Liked {likedDishes.length} dishes</div>
          <div className="flex space-x-4 mb-4 relative">
            {likedDishes.map((dish, index) => (
              <div key={index} className="border border-gray-200 rounded-lg flex w-64">
                <div className="flex-grow p-3">
                  <div className="font-medium">{dish.name}</div>
                  <div className="text-gray-500">Rs. {dish.price}</div>
                </div>
                <div className="w-20 h-20 relative">
                  <img
                    src={dish.image || "/api/placeholder/80/80"}
                    alt={dish.name}
                    className="rounded-tr-lg rounded-br-lg object-cover w-full h-full"
                  />
                  <button className="absolute bottom-2 right-2 bg-white rounded-full p-1 shadow-md">
                    <Plus size={16} />
                  </button>
                </div>
              </div>
            ))}
            <button className="absolute top-1/2 -translate-y-1/2 -left-8 bg-white rounded-full p-2 shadow-md">
              <ChevronLeft size={20} />
            </button>
            <button className="absolute top-1/2 -translate-y-1/2 -right-8 bg-white rounded-full p-2 shadow-md">
              <ChevronRight size={20} />
            </button>
          </div>
        </>
      )}

      <button className="flex items-center text-gray-500 mt-2">
        <ThumbsUp size={18} className="mr-1" />
        <span>Helpful</span>
      </button>
    </div>
  );
};

const AllReviewModal = ({ isOpen, onClose, restaurantName }: AllReviewModalProps) => {
  const [activeTab, setActiveTab] = useState('top');

  if (!isOpen) return null;

  // Example data
  const averageRating = 4.5;
  const totalReviews = 100;
  const ratingCounts = [
    { rating: 5, count: 65 },
    { rating: 4, count: 20 },
    { rating: 3, count: 10 },
    { rating: 2, count: 3 },
    { rating: 1, count: 2 }
  ];

  const reviews = [
    {
      reviewer: "Malik",
      rating: 5,
      timeAgo: "1 week ago",
      reviewText: "There was no complimentary green chutney with both; roll paratha and chicken piece, taste was good",
      likedDishes: [
        {
          name: "Chicken Jumbo Paratha Roll",
          price: 535,
          image: `${assets.ParathaRoll}`
        },
        {
          name: "Chicken Breast Tikka",
          price: 480,
          image: `${assets.ChickenBreastTikka}`
        }
      ]
    },
    {
      reviewer: "Zoya",
      rating: 4,
      timeAgo: "2 weeks ago",
      reviewText: "Food was delicious but delivery took longer than expected."
    }
  ];

  return (
    <div className="fixed inset-0 backdrop-blur flex items-center justify-center z-50 " style={{ backgroundColor: '#00000033' }}>
      {/* Modal Container */}
      <div className="bg-gray-100 rounded-lg w-fit max-w-2xl max-h-[80vh] overflow-y-auto shadow-lg">
        <div className=' bg-gray-100'>
          <div className="p-2 border-gray-200 flex justify-between items-center sticky top-0 bg-white">
            <h2 className="text-xl font-semibold">Reviews</h2>
            <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-100">
              <X size={24} className="text-gray-500" />
            </button>
          </div>

          <div className="p-2">
            <div className="text-md text-[#637381]">{restaurantName}</div>
          </div>
        </div>

        {/* Rating and Review Count */}
        <div className='bg-white p-4 m-2.5 rounded-lg border border-gray-200 '>
          <div className="p-6 flex  border-gray-200">
            <div className="mr-8 text-center">
              <div className="text-4xl font-bold mt-6">{averageRating}</div>
              <div className="flex text-yellow-400 justify-center my-1">
                {[...Array(5)].map((_, i) => (
                  <span key={i} className={i < Math.floor(averageRating) ? "" : (i < averageRating ? "text-yellow-400" : "text-gray-300")}>★</span>
                ))}
              </div>
              <div className="text-sm text-gray-500">All Ratings ({totalReviews}+)</div>
            </div>

            <div className="flex-grow">
              {ratingCounts.map((item) => (
                <RatingBar
                  key={item.rating}
                  rating={item.rating}
                  count={item.count}
                  maxCount={Math.max(...ratingCounts.map(r => r.count))}
                />
              ))}
            </div>
          </div>
        </div>

        <div className="p-4 flex gap-2 border-b border-gray-200 overflow-x-auto">
          <button
            className={`px-4 py-2 rounded-full text-sm ${activeTab === 'top' ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-700'}`}
            onClick={() => setActiveTab('top')}
          >
            Top reviews
          </button>
          <button
            className={`px-4 py-2 rounded-full text-sm ${activeTab === 'newest' ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-700'}`}
            onClick={() => setActiveTab('newest')}
          >
            Newest
          </button>
          <button
            className={`px-4 py-2 rounded-full text-sm ${activeTab === 'highest' ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-700'}`}
            onClick={() => setActiveTab('highest')}
          >
            Highest rating
          </button>
          <button
            className={`px-4 py-2 rounded-full text-sm ${activeTab === 'lowest' ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-700'}`}
            onClick={() => setActiveTab('lowest')}
          >
            Lowest rating
          </button>
        </div>

        <div className=" p-6 border-gray-300 bg-white m-2.5 rounded-lg border">
          {/* Review Cards */}
          {reviews.map((review, index) => (
            
            <ReviewCard 
              key={index}
              reviewer={review.reviewer}
              rating={review.rating}
              timeAgo={review.timeAgo}
              reviewText={review.reviewText}
              likedDishes={review.likedDishes}
            />
          ))}
        </div>
      </div>
    </div>
  );
};


export default AllReviewModal;