/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useMemo } from "react";
import { Star, X } from "lucide-react";

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  restaurantName: string;
  restaurantImage: string;
  rating: number;
  reviewCount: number;
  restaurantId: string;
  // Product data passed directly through props
  productData?: any | null; // Changed to any to handle both product and device data
}

interface Review {
  _id: string;
  rating?: number;
  comment?: string;
  customerName?: string;
  userId?: {
    name?: string;
    email?: string;
  };
  createdDate?: string;
  createdAt?: string;
  // Add device review structure
  food?: number;
  service?: number;
  ambiance?: number;
  customerId?: string;
  testimonial?: string;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  isOpen,
  onClose,
  restaurantName,
  restaurantImage,
  rating,
  reviewCount,
  productData = null,
}) => {
  // Extract reviews from product data - handle both product and device data structures
  const reviews = useMemo(() => {
    if (!productData) return [];

    // Check if it's device data with reviews array
    if (productData.reviews && Array.isArray(productData.reviews)) {
      return productData.reviews;
    }

    // Check if it's product data with reviewId array
    if (productData.reviewId && Array.isArray(productData.reviewId)) {
      return productData.reviewId;
    }

    return [];
  }, [productData]);

  if (!isOpen) {
    return null;
  }

  // Calculate average rating from reviews - handle both rating structures
  const calculateAverageRating = (reviewsArray: Review[]) => {
    if (reviewsArray.length === 0) return 0;

    const totalRating = reviewsArray.reduce((sum, review) => {
      // Handle device review structure (food, service, ambiance)
      if (review.food !== undefined && review.service !== undefined && review.ambiance !== undefined) {
        return sum + ((review.food + review.service + review.ambiance) / 3);
      }
      // Handle product review structure (rating)
      if (review.rating !== undefined) {
        return sum + review.rating;
      }
      return sum;
    }, 0);

    return totalRating / reviewsArray.length;
  };

  // Generate rating bars based on reviews from product data
  const generateRatingBars = () => {
    if (reviews && reviews.length > 0) {
      return reviews.slice(0, 5).map((review: Review) => {
        let reviewRating = 0;
        let customerName = "Anonymous";
        let comment = "";
        let createdDate = "";

        // Handle device review structure
        if (review.food !== undefined && review.service !== undefined && review.ambiance !== undefined) {
          reviewRating = (review.food + review.service + review.ambiance) / 3;
          comment = review.testimonial || "";
          createdDate = review.createdDate || "";
          // You might want to fetch customer name using customerId if needed
          customerName = "Customer"; // Default for device reviews
        }
        // Handle product review structure
        else if (review.rating !== undefined) {
          reviewRating = review.rating;
          customerName = review.customerName ||
            (review.userId && typeof review.userId === "object" && "name" in review.userId ? review.userId.name : undefined) ||
            "Anonymous";
          comment = review.comment || "";
          createdDate = review.createdAt || "";
        }

        return {
          id: review._id || Math.random().toString(),
          rating: reviewRating,
          percentage: (reviewRating / 5) * 100,
          customerName,
          comment,
          createdAt: createdDate
        };
      });
    } else {
      return [];
    }
  };

  const ratingBars = generateRatingBars();

  // Calculate display values - prioritize product data over props
  const displayAverageRating = reviews.length > 0
    ? calculateAverageRating(reviews)
    : rating;
  const displayTotalReviews = reviews.length > 0
    ? reviews.length
    : reviewCount;

  // Use product image from data if available, otherwise use provided restaurantImage
  const displayImage = productData?.Product_pic || productData?.image || restaurantImage;
  const displayName = productData?.name || restaurantName;


  return (
    <div
      className="fixed inset-0 backdrop-blur flex items-center justify-center z-50 px-2"
      style={{ backgroundColor: "#00000033" }}
    >
      <div className="relative w-full max-w-[700px] bg-white rounded-lg shadow-lg">
        {/* Header with close button */}
        <div className="flex items-center justify-between p-4">
          <h3 className="text-xl font-semibold text-[#19191C]">Reviews</h3>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Restaurant/Product name */}
        <div className="px-4 pb-5">
          <h4 className="text-md text-[#637381]">{displayName}</h4>
        </div>

        <div className="px-4 py-1 flex flex-col md:flex-row md:flex-nowrap items-start mt-3">
          {/* Product image */}
          <div className="w-full md:w-[250px] h-[158px] overflow-hidden rounded-lg flex-shrink-0">

            {/* Overall rating */}
            <div className="flex flex-col flex-wrap justify-center items-start gap-2 px-4">
              <div className="flex items-center">
                {/* <Star size={18} fill="#FF6B00" color="#FF6B00" /> */}
                <span className="ml-1 font-bold text-2xl text-[#19191C]">
                  {displayAverageRating.toFixed(1)}
                </span>
              </div>
              <div className="px-3 py-1 bg-orange-500 rounded-full text-white text-xs font-medium">
                {displayTotalReviews}+
              </div>



              {ratingBars.length > 0 ? (
                <div className="space-y-3">
                  {ratingBars.map((bar: {
                    id: string;
                    rating: number;
                    percentage: number;
                    customerName: string;
                    comment: string;
                    createdAt: string;
                  }) => (
                    <div key={bar.id} className="flex items-center w-full">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          size={18}
                          fill={star <= Math.round(bar.rating) ? "#FF6B00" : "none"}
                          color="#FF6B00"
                          className="mr-0.5"
                        />
                      ))}
                    </div>
                  ))}
                </div>
              ) : (
                <img
                  src={displayImage}
                  alt={displayName}
                  className="w-full h-full object-cover rounded-lg"
                  onError={(e) => {
                    // Fallback image in case the provided image fails to load
                    e.currentTarget.src = "https://via.placeholder.com/422x158?text=Product+Image";
                  }}
                />
              )}
              <button className="text-blue-500 hover:text-blue-700 cursor-pointer text-sm font-medium transition-colors">
                See Reviews
              </button>
            </div>
          </div>

          {/* Review ratings */}
          <div className="w-full">
            {ratingBars.length > 0 ? (
              <div className="space-y-3">
                {ratingBars.map((bar: {
                  id: string;
                  rating: number;
                  percentage: number;
                  customerName: string;
                  comment: string;
                  createdAt: string;
                }) => (
                  <div key={bar.id} className="flex items-center w-full">
                    <div className="md:w-20 w-fit text-right md:mr-3 mr-1 flex-shrink-0">
                      <span className="text-[#0A0A0A] text-sm font-light">
                        {bar.customerName}
                      </span>
                    </div>
                    <div className="flex items-center w-[18px] h-[20px] mr-2 flex-shrink-0">
                      <Star size={18} fill="#FF6B00" color="#FF6B00" />
                    </div>
                    <div className="flex-grow h-2 bg-gray-200 relative rounded-full">
                      <div
                        className="h-full bg-primary rounded-full transition-all duration-300"
                        style={{ width: `${bar.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center py-8">
                <div className="text-gray-500 text-center">
                  <div className="text-lg font-medium mb-2">No Reviews Yet</div>
                  <div className="text-sm">Be the first to review this product!</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Additional info sections */}
        <div className="px-4 py-3">
          <h5 className="font-medium text-[#19191C] mb-1">Delivery Fee</h5>
          <p className="text-[#637381] text-sm cursor-pointer hover:text-blue-500 transition-colors">
            More Info
          </p>
        </div>

        <div className="px-4 py-3">
          <h5 className="font-medium text-[#19191C] mb-1">Minimum Order</h5>
          <p className="text-[#637381] text-sm cursor-pointer hover:text-blue-500 transition-colors">
            More Info
          </p>
        </div>
      </div>
    </div>
  );
};

export default ReviewModal;