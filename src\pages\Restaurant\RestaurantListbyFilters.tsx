import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Search } from 'lucide-react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { useRestaurantData } from './hooks/useRestaurantData';
import { useRestaurantFilters } from './hooks/useRestaurantFilters';
import ResNavbar from './RestaurentListNavbar';
import FilterSidebar from '../../components/RestaurentListbyFilterComponent/FilterSidebar';
import SearchResults from '../../components/RestaurentListbyFilterComponent/SearchResults';
import DailyDeals from '../../components/RestaurentListbyFilterComponent/DailyDeals';
import RestaurantGrid from '../../components/RestaurentListbyFilterComponent/RestaurantGrid';
import HorizontalRestaurantList from '../../components/RestaurentListbyFilterComponent/HorizontalRestaurantList';
// import CuisineCard from '../components/RestaurentListbyFilterComponent/CuisineCard';
import { addFavorite, getFavoriteByCustomerId, selectFavoriteLoading } from '../../redux-store/slices/deviceSlice';
// import { getProductsWithParentCategory, selectProducts } from '../../redux-store/slices/productSlice';

const RestaurantListbyFilters: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Custom hooks
  const { restaurants, loading, error, addressFilter } = useRestaurantData();
  const { activeFilters, setActiveFilters, filteredRestaurants, calculateAverageRating } = useRestaurantFilters(restaurants);

  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [visibleRestaurants, setVisibleRestaurants] = useState(12);
  const [showFiltersMobile, setShowFiltersMobile] = useState(false);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  const favoriteLoading = useSelector(selectFavoriteLoading);
  const userId = useSelector((state: any) => state.customer?.currentCustomer?._id);
  // const products = useSelector(selectProducts);

  const isMobile = windowWidth < 768;

  // Effects
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);


  // useEffect(() => {
  //   if (userId) {
  //     dispatch(getProductsWithParentCategory({ userId }) as any);
  //   }
  // }, [dispatch, userId]);

  useEffect(() => {
    if (userId) {
      dispatch(getFavoriteByCustomerId(userId) as any);
    }
  }, [dispatch, userId]);

  useEffect(() => {
    setVisibleRestaurants(12);
  }, [activeFilters, searchQuery]);

  // Business type categories
  const businessTypeCategories = useMemo(() => {
    const businessTypeMap = new Map();
    restaurants.forEach(restaurant => {
      if (restaurant.businessType) {
        const businessType = restaurant.businessType.toLowerCase();
        if (!businessTypeMap.has(businessType)) {
          businessTypeMap.set(businessType, {
            name: restaurant.businessType,
            image: restaurant.image || 'https://patronpal.com/assets/beverage.jpeg',
            categoryId: businessType,
            children: []
          });
        }
      }
    });
    return Array.from(businessTypeMap.values());
  }, [restaurants]);


  // const cuisineCategories = useMemo(() => {
  //   const cuisineMap = new Map();

  //   products.forEach(product => {
  //     if (product.categoryParents && product.categoryParents.length > 0) {
  //       product.categoryParents.forEach((parent: any) => {
  //         if (!cuisineMap.has(parent._id)) {
  //           cuisineMap.set(parent._id, {
  //             _id: parent._id,
  //             name: parent.name,
  //             image: product.Product_pic || 'https://patronpal.com/assets/cuisine.jpeg',
  //             categoryId: parent._id
  //           });
  //         }
  //       });
  //     }
  //   });

  //   return Array.from(cuisineMap.values());
  // }, [products]);

  // Filtered restaurant collections
  const recommendedRestaurants = useMemo(() => {
    return restaurants
      .filter(r => {
        const rating = calculateAverageRating(r.reviews);
        return rating && rating >= 4;
      })
      .sort((a, b) => {
        const ratingA = calculateAverageRating(a.reviews) || 0;
        const ratingB = calculateAverageRating(b.reviews) || 0;
        return ratingB - ratingA;
      });
  }, [restaurants, calculateAverageRating]);

  const freeDeliveryRestaurants = useMemo(() => {
    return restaurants.filter(r =>
      (r.ChargesFreeKm && r.ChargesFreeKm > 0) ||
      (r.delivery === true || r.delivery === "true")
    );
  }, [restaurants]);

  const searchFilteredRestaurants = useMemo(() => {
    if (!searchQuery.trim()) return restaurants;
    const query = searchQuery.toLowerCase();
    return restaurants.filter(restaurant =>
      restaurant.name.toLowerCase().includes(query) ||
      restaurant.businessType?.toLowerCase().includes(query) ||
      restaurant.City.toLowerCase().includes(query) ||
      restaurant.State.toLowerCase().includes(query)
    );
  }, [restaurants, searchQuery]);

  // Event handlers
  const handleServiceTypeChange = (option: string) => {
    const serviceTypeParam = option.toLowerCase();
    const newParams = new URLSearchParams(searchParams);
    newParams.set('type', serviceTypeParam);
    navigate(`/all-restaurants?${newParams.toString()}`, { replace: true });
  };

  const handleFavoriteToggle = (restaurantId: string) => {
    if (!userId) {
      navigate('/login');
      return;
    }
    dispatch(addFavorite({ deviceId: restaurantId, customerId: userId }) as any);
  };

  const handleShowMore = () => setVisibleRestaurants(prev => prev + 12);

  const handleFilterChange = (filterType: string, value: string) => {
    switch (filterType) {
      case 'sort':
        setActiveFilters({ ...activeFilters, sort: value });
        break;
      case 'offers':
        const offers = [...activeFilters.offers];
        if (offers.includes(value)) {
          setActiveFilters({ ...activeFilters, offers: offers.filter(offer => offer !== value) });
        } else {
          setActiveFilters({ ...activeFilters, offers: [...offers, value] });
        }
        break;
      case 'businessTypes':
        const businessTypes = [...activeFilters.businessTypes];
        if (businessTypes.includes(value)) {
          setActiveFilters({ ...activeFilters, businessTypes: businessTypes.filter(type => type !== value) });
        } else {
          setActiveFilters({ ...activeFilters, businessTypes: [...businessTypes, value] });
        }
        break;
      case 'cuisines': // Add this new case
        const cuisines = [...(activeFilters.cuisines || [])];
        if (cuisines.includes(value)) {
          setActiveFilters({ ...activeFilters, cuisines: cuisines.filter(cuisine => cuisine !== value) });
        } else {
          setActiveFilters({ ...activeFilters, cuisines: [...cuisines, value] });
        }
        break;
    }
  };

  // Loading and error states
  if (loading) {
    return (
      <div>
        <ResNavbar onOptionChange={handleServiceTypeChange} />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading Restaurants...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || restaurants.length === 0) {
    return (
      <div>
        <ResNavbar onOptionChange={handleServiceTypeChange} />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 py-12">
          <div className="bg-white rounded-xl shadow-sm p-8 max-w-md w-full text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-3">No Restaurants Found</h2>
            <p className="text-gray-600 mb-6">
              {addressFilter
                ? `We couldn't find any restaurants near "${addressFilter}".`
                : "We couldn't find any restaurants matching your current filters."
              }
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <ResNavbar onOptionChange={handleServiceTypeChange} />
      <div className="flex bg-gray-50 py-2 mt-1 min-h-screen relative md:px-20 px-3">

        {/* Filter Sidebar */}
        {(!isMobile || showFiltersMobile) && (
          <div className='bg-white h-fit rounded-xl'>
            <FilterSidebar
              activeFilters={activeFilters}
              businessTypeCategories={businessTypeCategories}
              isMobile={isMobile}
              showFiltersMobile={showFiltersMobile}
              onSortChange={(value) => handleFilterChange('sort', value)}
              onOfferChange={(value) => handleFilterChange('offers', value)}
              onBusinessTypeChange={(value) => handleFilterChange('businessTypes', value)}
              onToggleFiltersMobile={() => setShowFiltersMobile(!showFiltersMobile)}
            />
          </div>
        )}

        {/* Main Content */}
        <div className={`flex-1 overflow-hidden px-3 py-0 bg-gray-50 ${isMobile ? 'w-full' : ''}`}>

          {/* Search Bar */}
          <div className="relative mb-6 flex justify-center w-full">
            <Search className="absolute left-3 md:top-2.5 top-2 w-5 h-5 text-gray-700" />
            <input
              type="text"
              placeholder="Search Restaurants, Business Types or Locations"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 md:py-2 py-1 w-full bg-white rounded-lg text-gray-700 outline-none"
            />

            {isMobile && <button onClick={() => setShowFiltersMobile(!showFiltersMobile)} >
              <svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_7401_2095)">
                  <rect x="4" width="48" height="48" rx="24" fill="white" shape-rendering="crispEdges" />
                  <path d="M39 19.25H33C32.59 19.25 32.25 18.91 32.25 18.5C32.25 18.09 32.59 17.75 33 17.75H39C39.41 17.75 39.75 18.09 39.75 18.5C39.75 18.91 39.41 19.25 39 19.25Z" fill="#292D32" />
                  <path d="M23 19.25H19C18.59 19.25 18.25 18.91 18.25 18.5C18.25 18.09 18.59 17.75 19 17.75H23C23.41 17.75 23.75 18.09 23.75 18.5C23.75 18.91 23.41 19.25 23 19.25Z" fill="#292D32" />
                  <path d="M27 22.75C24.66 22.75 22.75 20.84 22.75 18.5C22.75 16.16 24.66 14.25 27 14.25C29.34 14.25 31.25 16.16 31.25 18.5C31.25 20.84 29.34 22.75 27 22.75ZM27 15.75C25.48 15.75 24.25 16.98 24.25 18.5C24.25 20.02 25.48 21.25 27 21.25C28.52 21.25 29.75 20.02 29.75 18.5C29.75 16.98 28.52 15.75 27 15.75Z" fill="#292D32" />
                  <path d="M39 30.25H35C34.59 30.25 34.25 29.91 34.25 29.5C34.25 29.09 34.59 28.75 35 28.75H39C39.41 28.75 39.75 29.09 39.75 29.5C39.75 29.91 39.41 30.25 39 30.25Z" fill="#292D32" />
                  <path d="M25 30.25H19C18.59 30.25 18.25 29.91 18.25 29.5C18.25 29.09 18.59 28.75 19 28.75H25C25.41 28.75 25.75 29.09 25.75 29.5C25.75 29.91 25.41 30.25 25 30.25Z" fill="#292D32" />
                  <path d="M31 33.75C28.66 33.75 26.75 31.84 26.75 29.5C26.75 27.16 28.66 25.25 31 25.25C33.34 25.25 35.25 27.16 35.25 29.5C35.25 31.84 33.34 33.75 31 33.75ZM31 26.75C29.48 26.75 28.25 27.98 28.25 29.5C28.25 31.02 29.48 32.25 31 32.25C32.52 32.25 33.75 31.02 33.75 29.5C33.75 27.98 32.52 26.75 31 26.75Z" fill="#292D32" />
                </g>
                <defs>
                  <filter id="filter0_d_7401_2095" x="0" y="0" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dy="4" />
                    <feGaussianBlur stdDeviation="2" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7401_2095" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7401_2095" result="shape" />
                  </filter>
                </defs>
              </svg>
            </button>}
          </div>

          {/* Content Sections */}
          {searchQuery.trim() ? (
            <SearchResults
              restaurants={searchFilteredRestaurants}
              searchQuery={searchQuery}
              visibleCount={visibleRestaurants}
              onShowMore={handleShowMore}
              hasMore={visibleRestaurants < searchFilteredRestaurants.length}
              isMobile={isMobile}
              userId={userId}
              onFavoriteToggle={handleFavoriteToggle}
              favoriteLoading={favoriteLoading}
            />
          ) : (
            <>
              <DailyDeals
                businessTypeCategories={businessTypeCategories}
                onBusinessTypeChange={(type) => handleFilterChange('businessTypes', type)}
                isMobile={isMobile}
              />

              {/* <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Popular Cuisines</h2>
                <div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-4 lg:grid-cols-6'} gap-4`}>
                  {cuisineCategories.map(cuisine => (
                    <div
                      key={cuisine._id}
                      className="bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => handleFilterChange('cuisines', cuisine.name)}
                    >
                      <img
                        src={cuisine.image}
                        alt={cuisine.name}
                        className="w-full h-20 object-cover rounded-md mb-2"
                      />
                      <h3 className="text-sm font-medium text-gray-800 text-center truncate">
                        {cuisine.name}
                      </h3>
                    </div>
                  ))}
                </div>
              </div> */}

              <HorizontalRestaurantList
                restaurants={recommendedRestaurants}
                title="Recommended For You"
                userId={userId}
                onFavoriteToggle={handleFavoriteToggle}
                favoriteLoading={favoriteLoading}
              />

              <HorizontalRestaurantList
                restaurants={freeDeliveryRestaurants}
                title="Free Delivery"
                userId={userId}
                onFavoriteToggle={handleFavoriteToggle}
                favoriteLoading={favoriteLoading}
              />

              <RestaurantGrid
                restaurants={filteredRestaurants}
                visibleCount={visibleRestaurants}
                onShowMore={handleShowMore}
                hasMore={visibleRestaurants < filteredRestaurants.length}
                isMobile={isMobile}
                userId={userId}
                onFavoriteToggle={handleFavoriteToggle}
                favoriteLoading={favoriteLoading}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default RestaurantListbyFilters;