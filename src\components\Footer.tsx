import { assets } from "../assets/assets";

const Footer = () => {
  return (
    <footer className="bg-white py-9 md:px-8 lg:px-12 px-4 w-full">
      <div className="max-w-7xl justify-between mx-auto">
        {/* Main Footer Content */}
        <div className="flex flex-col lg:flex-row justify-between  lg:gap-10 mb-12">
          {/* Left Section - Brand and Apps */}
          <div className="lg:max-w-xs">
            <div className="text-2xl font-bold mb-6">
              Patron<span className="text-orange-500">Pal</span>   
            </div>
            
            {/* App Store Buttons */}
            <div className="flex flex-row mb-6">
              <div className=" ">
                <div className="mr-3">
                 <img src={assets.mac_image} alt="" className=" w-[133px] h-[45.656715393066406px]" />
                </div>
                </div>
              
              <div className=" ">
                <div className="mr-3">
                  <img src={assets.PlayStore_image} alt="" className=" w-[133px] h-[45.656715393066406px]" />
                </div>
              </div>
            </div>
            
            {/* Social Media Icons */}
            <div className="flex gap-3">
              <a href="#" className="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="#" className="bg-pink-500 text-white p-2 rounded-full hover:bg-pink-600 transition-colors">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.024-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.986C24.007 5.367 18.641.001.012.001z"/>
                </svg>
              </a>
              <a href="#" className="bg-red-600 text-white p-2 rounded-full hover:bg-red-700 transition-colors">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </a>
            </div>
          </div>
          
          {/* Right Section - Navigation Links */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6  lg:pt-10 md:pt-0 pt-4 lg:gap-8 flex-1">
            {/* Product */}
            <div>
              <h3 className="text-orange-500 font-semibold mb-4 text-lg">Product</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Features</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Pricing</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Case studies</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Reviews</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Updates</a></li>
              </ul>
            </div>
            
            {/* Story */}
            <div>
              <h3 className="text-orange-500 font-semibold mb-4 text-lg">Story</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">About</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Contact us</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Careers</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Culture</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Blog</a></li>
              </ul>
            </div>
            
            {/* About Us */}
            <div>
              <h3 className="text-orange-500 font-semibold mb-4 text-lg">About Us</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Teams</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Help center</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Server status</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Report a bug</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Chat support</a></li>
              </ul>
            </div>
            
            {/* Downloads */}
            <div>
              <h3 className="text-orange-500 font-semibold mb-4 text-lg">Downloads</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">iOS</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Android</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Mac</a></li>
                <li><a href="#" className="text-[#090909] hover:text-orange-500 transition-colors">Windows</a></li>
              </ul>
            </div>
          </div>
        </div>
        
        {/* Bottom Copyright Section */}
        <div className="pt-8 border-t border-gray-200">
          <div className="text-center text-[#090909]">
            Copyright © PatronPal | All Rights Reserved | 
            <a href="#" className="hover:text-orange-500 transition-colors ml-1">Terms and Conditions</a> | 
            <a href="#" className="hover:text-orange-500 transition-colors ml-1">Privacy Policy</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;