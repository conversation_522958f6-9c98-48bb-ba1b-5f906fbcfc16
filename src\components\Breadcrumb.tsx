import { Link } from "react-router-dom";
import { ChevronRight } from "lucide-react";

interface BreadcrumbProps {
  items: { label: string; path: string }[];
}

// example use
//  const breadcrumbItems = [
//     { label: "Home", path: "/" },
//     { label: "Chicago", path: "/chicago" },
//     { label: restaurantData.name, path: `/restaurant/${restaurantData.id}` },
//   ];
//   ];

{/* <div>
    <Breadcrumb items={breadcrumbItems} />
</div> */}

const Breadcrumb = ({ items }: BreadcrumbProps) => {
  return (
    <div className="container mx-auto px-4 pt-4.5">
      <nav className="flex" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-2">
          {items.map((item, index) => (
            <li key={item.path} className="inline-flex items-center">
              {index > 0 && (
                <ChevronRight className="mx-1 h-4 w-4 text-gray-400" />
              )}
              <Link
                to={item.path}
                className={`text-base ${
                  index === items.length - 1
                    ? "text-gray-500 font-medium"
                    : "text-gray-700 hover:text-gray-900"
                }`}
              >
                {item.label}
              </Link>
            </li>
          ))}
        </ol>
      </nav>
    </div>
  );
};

export default Breadcrumb;