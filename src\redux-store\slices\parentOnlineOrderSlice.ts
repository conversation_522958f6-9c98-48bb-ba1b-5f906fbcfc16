/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import apiClient from "../config";

// Types
export interface OnlineOrderItem {
  _id: string;
  customerId: any;
  userId: any;
  // Add other properties based on your OnlineOrderItem model
}

export interface PatronPalCustomer {
  _id: string;
  CustomerLoyalty: {
    Points: number;
  };
  // Add other customer properties
}

export interface ParentOnlineOrder {
  _id: string;
  OrderNo: string;
  subOnlineOrderId: OnlineOrderItem[] | string[];
  customerId: PatronPalCustomer | string;
  totalAmount: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateParentOnlineOrderRequest {
  customerId: string;
  subOnlineOrderId: string[];
  totalAmount: number;
}

export interface CreateParentOnlineOrderResponse {
  OrderNo: string;
  subOnlineOrderId: string[];
  customerId: string;
  totalAmount: number;
  onlineOrderItems: OnlineOrderItem[];
}

export interface UpdateParentOnlineOrderRequest {
  _id: string;
  data: Partial<ParentOnlineOrder>;
}

export interface ParentOnlineOrderState {
  orders: ParentOnlineOrder[];
  currentOrder: ParentOnlineOrder | null;
  customerOrders: ParentOnlineOrder[];
  loading: boolean;
  error: string | null;
  createOrderResponse: CreateParentOnlineOrderResponse | null;
}

const initialState: ParentOnlineOrderState = {
  orders: [],
  currentOrder: null,
  customerOrders: [],
  loading: false,
  error: null,
  createOrderResponse: null,
};

// Async Thunks
export const fetchAllParentOnlineOrders = createAsyncThunk(
  "parentOnlineOrder/fetchAll",
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get("/online-order"); // Adjust endpoint as needed
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch orders"
      );
    }
  }
);

export const fetchParentOnlineOrdersByCustomerId = createAsyncThunk(
  "parentOnlineOrder/fetchByCustomerId",
  async (customerId: string, { rejectWithValue }) => {
    try {
      const response = await apiClient.get(
        `/online-order/${customerId}`
      );
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch customer orders"
      );
    }
  }
);

export const createParentOnlineOrder = createAsyncThunk(
  "parentOnlineOrder/create",
  async (orderData: CreateParentOnlineOrderRequest, { rejectWithValue }) => {
    try {
      const response = await apiClient.post("/online-order", orderData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to create order"
      );
    }
  }
);

export const updateParentOnlineOrder = createAsyncThunk(
  "parentOnlineOrder/update",
  async (
    { _id, data }: UpdateParentOnlineOrderRequest,
    { rejectWithValue }
  ) => {
    try {
      const response = await apiClient.put(
        `/online-order/${_id}`,
        data
      );
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to update order"
      );
    }
  }
);

export const deleteParentOnlineOrder = createAsyncThunk(
  "parentOnlineOrder/delete",
  async (_id: string, { rejectWithValue }) => {
    try {
      const response = await apiClient.delete(`/parent-online-orders/${_id}`);
      return { _id, ...response.data };
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to delete order"
      );
    }
  }
);

// Slice
const parentOnlineOrderSlice = createSlice({
  name: "parentOnlineOrder",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentOrder: (state) => {
      state.currentOrder = null;
    },
    clearCreateOrderResponse: (state) => {
      state.createOrderResponse = null;
    },
    setCurrentOrder: (state, action: PayloadAction<ParentOnlineOrder>) => {
      state.currentOrder = action.payload;
    },
    resetOrders: (state) => {
      state.orders = [];
      state.customerOrders = [];
      state.currentOrder = null;
      state.createOrderResponse = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch All Orders
      .addCase(fetchAllParentOnlineOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllParentOnlineOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload;
      })
      .addCase(fetchAllParentOnlineOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Fetch Orders by Customer ID
      .addCase(fetchParentOnlineOrdersByCustomerId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchParentOnlineOrdersByCustomerId.fulfilled,
        (state, action) => {
          state.loading = false;
          state.customerOrders = action.payload;
        }
      )
      .addCase(
        fetchParentOnlineOrdersByCustomerId.rejected,
        (state, action) => {
          state.loading = false;
          state.error = action.payload as string;
        }
      )

      // Create Order
      .addCase(createParentOnlineOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createParentOnlineOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.createOrderResponse = action.payload;
        // Optionally add to orders array if needed
        // state.orders.push(action.payload);
      })
      .addCase(createParentOnlineOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update Order
      .addCase(updateParentOnlineOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateParentOnlineOrder.fulfilled, (state, action) => {
        state.loading = false;
        const updatedOrder = action.payload.updatedData;
        if (updatedOrder) {
          // Update in orders array
          const orderIndex = state.orders.findIndex(
            (order) => order._id === updatedOrder._id
          );
          if (orderIndex !== -1) {
            state.orders[orderIndex] = updatedOrder;
          }

          // Update in customerOrders array
          const customerOrderIndex = state.customerOrders.findIndex(
            (order) => order._id === updatedOrder._id
          );
          if (customerOrderIndex !== -1) {
            state.customerOrders[customerOrderIndex] = updatedOrder;
          }

          // Update current order if it's the same
          if (state.currentOrder?._id === updatedOrder._id) {
            state.currentOrder = updatedOrder;
          }
        }
      })
      .addCase(updateParentOnlineOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete Order
      .addCase(deleteParentOnlineOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteParentOnlineOrder.fulfilled, (state, action) => {
        state.loading = false;
        const deletedId = action.payload._id;

        // Remove from orders array
        state.orders = state.orders.filter((order) => order._id !== deletedId);

        // Remove from customerOrders array
        state.customerOrders = state.customerOrders.filter(
          (order) => order._id !== deletedId
        );

        // Clear current order if it was deleted
        if (state.currentOrder?._id === deletedId) {
          state.currentOrder = null;
        }
      })
      .addCase(deleteParentOnlineOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  clearCurrentOrder,
  clearCreateOrderResponse,
  setCurrentOrder,
  resetOrders,
} = parentOnlineOrderSlice.actions;

export default parentOnlineOrderSlice.reducer;

// Selectors
export const selectAllParentOnlineOrders = (state: {
  parentOnlineOrder: ParentOnlineOrderState;
}) => state.parentOnlineOrder.orders;

export const selectCustomerOrders = (state: {
  parentOnlineOrder: ParentOnlineOrderState;
}) => state.parentOnlineOrder.customerOrders;

export const selectCurrentOrder = (state: {
  parentOnlineOrder: ParentOnlineOrderState;
}) => state.parentOnlineOrder.currentOrder;

export const selectParentOnlineOrderLoading = (state: {
  parentOnlineOrder: ParentOnlineOrderState;
}) => state.parentOnlineOrder?.loading;

export const selectParentOnlineOrderError = (state: {
  parentOnlineOrder: ParentOnlineOrderState;
}) => state.parentOnlineOrder.error;

export const selectCreateOrderResponse = (state: {
  parentOnlineOrder: ParentOnlineOrderState;
}) => state.parentOnlineOrder.createOrderResponse;
