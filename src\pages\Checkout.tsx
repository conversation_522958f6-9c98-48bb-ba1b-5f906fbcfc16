/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import type { AppDispatch } from '../redux-store/store';
import { useNavigate } from 'react-router-dom';
import { selectCartItems, selectCartTotal, clearCart } from '../redux-store/slices/cartSlice';
import { selectCurrentCustomer } from '../redux-store/slices/customerSlice';
import {
    createParentOnlineOrder,
    selectParentOnlineOrderLoading,
    selectParentOnlineOrderError,
    selectCreateOrderResponse,
    clearCreateOrderResponse,
    clearError as clearParentOrderError
} from '../redux-store/slices/parentOnlineOrderSlice';
import {
    createOnlineOrder,
    selectLoading as selectSubOrderLoading,
    selectError as selectSubOrderError,
    clearError as clearSubOrderError
} from '../redux-store/slices/subOnlineOrderitemSlice';
import CartSummary from '../components/CartSummary';
import CheckoutForm from '../components/CheckoutForm';

const Checkout: React.FC = () => {
    // Get current customer from Redux store
    const currentCustomer = useSelector(selectCurrentCustomer);

    const [firstName, setFirstName] = useState(currentCustomer?.fname);
    const [lastName, setLastName] = useState(currentCustomer?.lname);
    const [mobileNumber, setMobileNumber] = useState(currentCustomer?.Phone);
    const [email, setEmail] = useState(currentCustomer?.Email);
    const [selectedMethod, setSelectedMethod] = useState('');
    const [orderTypes, setOrderTypes] = useState('');
    const [showSuccessAlert, setShowSuccessAlert] = useState(false);
    const [isProcessingOrder, setIsProcessingOrder] = useState(false);

    useEffect(() => {
        setFirstName(prev => prev || currentCustomer?.fname || '');
        setLastName(prev => prev || currentCustomer?.lname || '');
        setMobileNumber(prev => prev || currentCustomer?.Phone || '');
        setEmail(prev => prev || currentCustomer?.Email || '');
    }, []);

    const dispatch = useDispatch<AppDispatch>();
    const navigate = useNavigate();

    // Get cart data from Redux store
    const cartItems = useSelector(selectCartItems);
    const cartTotal = useSelector(selectCartTotal);

    // Get order state from Redux store
    const isParentOrderLoading = useSelector(selectParentOnlineOrderLoading);
    const parentOrderError = useSelector(selectParentOnlineOrderError);
    const createOrderResponse = useSelector(selectCreateOrderResponse);

    // Get sub-order state from Redux store
    const isSubOrderLoading = useSelector(selectSubOrderLoading);
    const subOrderError = useSelector(selectSubOrderError);

    // Get URL parameter on component mount
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const type = urlParams.get('type') || '';
        console.log('Type from URL:', type);
        setOrderTypes(type);
    }, []);

    // Handle successful order creation
    useEffect(() => {
        if (createOrderResponse) {
            setShowSuccessAlert(true);
            setIsProcessingOrder(false);

            // Clear cart after successful order
            dispatch(clearCart());

            // Auto-hide alert after 3 seconds and navigate
            setTimeout(() => {
                setShowSuccessAlert(false);
                dispatch(clearCreateOrderResponse());
                navigate(`/tracking?type=${selectedMethod}`);
            }, 3000);
        }
    }, [createOrderResponse, dispatch, navigate, selectedMethod]);

    // Handle parent order errors
    useEffect(() => {
        if (parentOrderError) {
            // alert(`Parent order failed: ${parentOrderError}`);
            console.log(`Parent order failed: ${parentOrderError}`);
            dispatch(clearParentOrderError());
            setIsProcessingOrder(false);
        }
    }, [parentOrderError, dispatch]);

    // Handle sub-order errors
    useEffect(() => {
        if (subOrderError) {
            // alert(`Sub-order failed: ${subOrderError}`);
            console.log(`Sub-order failed: ${subOrderError}`);
            dispatch(clearSubOrderError());
            setIsProcessingOrder(false);
        }
    }, [subOrderError, dispatch]);

    const handleSubmit = async () => {
        // For now, only handle COD orders
        if (selectedMethod !== 'cod') {
            alert('Currently only Cash on Delivery is supported');
            return;
        }

        if (!currentCustomer) {
            alert('Customer information is required');
            return;
        }

        if (cartItems.length === 0) {
            alert('Cart is empty');
            return;
        }

        setIsProcessingOrder(true);

        try {
            console.log('Starting order creation process...');
            console.log('Order type:', orderTypes);
            console.log('Cart items:', cartItems);

            // Step 1: Create sub-orders for each cart item
            const subOrderPromises = cartItems.map(async (item: any) => {
                const subOrderData = {
                    Status: 'active',
                    orderType: orderTypes.toLowerCase() as 'delivery' | 'pickup',
                    orderStatus: 'new order' as const,
                    product: [item], // Each sub-order contains one product
                    customerId: currentCustomer._id,
                    userId: currentCustomer._id, // Assuming userId is same as customerId
                    PaymentStatus: 'cash' as const,
                    Amount: item.totalPrice || item.price,
                    deliveryfee: orderTypes.toLowerCase() === 'delivery' ? 50 : 0, // Example delivery fee
                    
                    // Add delivery options if it's a delivery order
                    ...(orderTypes.toLowerCase() === 'delivery' && {
                        deliveryOptions: {
                            dropOffAddress: currentCustomer.address,
                            dropOffInstructions: 'Please call upon arrival',
                            expressDelivery: 'no'
                        }
                    }),
                    
                    // Add pickup options if it's a pickup order
                    ...(orderTypes.toLowerCase() === 'pickup' && {
                        pickUpObj: {
                            pickupAddress: 'Restaurant Address', // Replace with actual restaurant address
                            pickupOptions: {
                                standardPickup: true,
                                schedulePickup: false
                            }
                        }
                    })
                };

                console.log('Creating sub-order with data:', subOrderData);
                
                // Dispatch the create sub-order action and wait for response
                const result = await dispatch(createOnlineOrder(subOrderData));
                
                if (createOnlineOrder.fulfilled.match(result)) {
                    console.log('Sub-order created successfully:', result.payload);
                    return result.payload;
                } else {
                    console.error('Sub-order creation failed:', result.payload);
                    throw new Error(`Failed to create sub-order: ${result.payload}`);
                }
            });

            // Wait for all sub-orders to be created
            console.log('Waiting for all sub-orders to be created...');
            const createdSubOrders = await Promise.all(subOrderPromises);
            console.log('All sub-orders created:', createdSubOrders);

            // Step 2: Create parent order with sub-order IDs
            const subOrderIds = createdSubOrders.map((subOrder: { _id: any; }) => subOrder._id);
            
            const parentOrderData = {
                customerId: currentCustomer._id,
                subOnlineOrderId: subOrderIds,
                totalAmount: cartTotal,
                orderType: orderTypes,
                paymentMethod: selectedMethod,
                customerDetails: {
                    firstName,
                    lastName,
                    mobileNumber,
                    email,
                    address: orderTypes.toLowerCase() !== 'pickup' ? currentCustomer.address : null
                }
            };

            console.log('Creating parent order with data:', parentOrderData);

            // Dispatch the create parent order action
            dispatch(createParentOnlineOrder(parentOrderData));

        } catch (error) {
            console.error('Error in order creation process:', error);
            alert('Failed to place order. Please try again.');
            setIsProcessingOrder(false);
        }
    };

    // Calculate total loading state
    const isLoading = isParentOrderLoading || isSubOrderLoading || isProcessingOrder;

    return (
        <div className="min-h-screen bg-gray-50 p-5 relative">
            {/* Success Alert */}
            {showSuccessAlert && (
                <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
                    <div className="flex items-center">
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Order placed successfully! Redirecting to tracking...
                    </div>
                </div>
            )}

            {/* Processing Alert */}
            {isProcessingOrder && (
                <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg">
                    <div className="flex items-center">
                        <svg className="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing your order...
                    </div>
                </div>
            )}

            <div className="w-full mx-auto flex justify-center items-start gap-6 flex-wrap">
                {/* Main Form Section */}
                <CheckoutForm 
                    currentCustomer={currentCustomer}
                    orderTypes={orderTypes}
                    selectedMethod={selectedMethod}
                    setSelectedMethod={setSelectedMethod}
                    handleSubmit={handleSubmit}
                    isOrderLoading={isLoading}
                />

                {/* Cart Summary Section */}
                <div className="w-[550px] sticky top-1">
                    <h1 className="text-base font-bold mb-2 text-gray-900">Order Summary</h1>
                    <CartSummary />
                </div>
            </div>
        </div>
    );
};

export default Checkout;