/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { X, MapPin } from "lucide-react";
import { GoogleMap, Marker } from '@react-google-maps/api';
import { useState, useEffect } from 'react';

interface RestaurantData {
    Line2: any;
    Line1: any;
    name: string;
    address: string;
    City?: string;
    State?: string;
    Country?: string;
    Phoneno?: string;
    pickupStartTime?: string;
    pickupEndTime?: string;
    deliveryStartTime?: string;
    deliveryEndTime?: string;
    delivery?: string;
    ChargesFreeKm?: number;
    ChargesperKm?: number;
}

interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
    restaurantData?: RestaurantData;
}

const InfoModal = ({ isOpen, onClose, restaurantData }: ModalProps) => {
    const [mapPosition, setMapPosition] = useState<google.maps.LatLngLiteral | null>(null);
    const [isGeocoding, setIsGeocoding] = useState(false);

    // Format time to display (convert 24hr to 12hr format)
    const formatTime = (time?: string) => {
        if (!time) return "N/A";
        const [hours, minutes] = time.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;
        return `${displayHour}:${minutes} ${ampm}`;
    };

    // Check if restaurant is currently open
    const isCurrentlyOpen = () => {
        if (!restaurantData?.pickupStartTime || !restaurantData?.pickupEndTime) return false;

        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();

        const [startHour, startMin] = restaurantData.pickupStartTime.split(':').map(Number);
        const [endHour, endMin] = restaurantData.pickupEndTime.split(':').map(Number);

        const startTime = startHour * 60 + startMin;
        const endTime = endHour * 60 + endMin;

        // Handle overnight hours (like 23:00 to 01:30)
        if (endTime < startTime) {
            return currentTime >= startTime || currentTime <= endTime;
        }

        return currentTime >= startTime && currentTime <= endTime;
    };

    const currentlyOpen = isCurrentlyOpen();

    // Format delivery fee info
    const formatDeliveryFee = () => {
        if (!restaurantData?.ChargesFreeKm || !restaurantData?.ChargesperKm) {
            return "Contact restaurant for delivery fee information";
        }

        return `Free delivery up to ${restaurantData.ChargesFreeKm}km, then $${restaurantData.ChargesperKm}/km`;
    };

    const formatAddress = () => {
        if (!restaurantData) return "";
        const parts = [restaurantData.Country, restaurantData.Line1, restaurantData.Line2, restaurantData.City];
        const filteredParts = parts.filter(
            part =>
                part &&
                part.trim().toLowerCase() !== 'undefined' &&
                part.trim() !== ''
        );
        return filteredParts.join(', ');
    };

    // Geocode address when modal opens and restaurant data is available
    useEffect(() => {
        if (isOpen && restaurantData && window.google && window.google.maps && !isGeocoding) {
            const addressString = formatAddress();
            
            if (addressString.trim()) {
                setIsGeocoding(true);
                const geocoder = new window.google.maps.Geocoder();
                
                geocoder.geocode({ address: addressString }, (results: { geometry: { location: {
                    [x: string]: any; lng: () => any; 
}; }; }[], status: string) => {
                    console.log('Geocoding result:', status, results);
                    if (status === "OK" && results && results[0] && results[0].geometry) {
                        const position = {
                            lat: results[0].geometry.location.lat(),
                            lng: results[0].geometry.location.lng()
                        };
                        console.log('Setting map position:', position);
                        setMapPosition(position);
                    } else {
                        console.log('Geocoding failed:', status, 'for address:', addressString);
                        // Fallback to a default location
                        setMapPosition({ lat: 40.7128, lng: -74.0060 });
                    }
                    setIsGeocoding(false);
                });
            } else {
                // No valid address, use default location
                setMapPosition({ lat: 40.7128, lng: -74.0060 });
                setIsGeocoding(false);
            }
        }
    }, [isOpen, restaurantData]);

    // Reset map position when modal closes
    useEffect(() => {
        if (!isOpen) {
            setMapPosition(null);
            setIsGeocoding(false);
        }
    }, [isOpen]);

    // Move the early return AFTER all hooks have been called
    if (!isOpen) return null;

    const mapCenter = mapPosition || { lat: 40.7128, lng: -74.0060 };

    return (
        <div className="fixed inset-0 backdrop-blur flex items-center w-full h-full justify-center z-50 md:px-0 px-2"
            style={{ backgroundColor: '#00000033' }}
            onClick={onClose}>
            <div onClick={(e) => e.stopPropagation()}
                className="bg-white rounded-lg shadow-lg w-[700px] h-fit overflow-y-auto">

                {/* Header */}
                <div className="p-1 flex justify-between items-center mx-1.5">
                    <h2 className="text-xl font-bold">
                        {restaurantData?.name || "Restaurant Info"}
                    </h2>
                    <button onClick={onClose} className="text-[#19191C] hover:text-gray-700">
                        <X size={24} />
                    </button>
                </div>

                {/* Operating Hours */}
                <div className="flex flex-col items-start space-y-2 p-1 mx-1.5 mt-2.5">
                    <div className="flex justify-center items-center space-x-1">
                        <MapPin className="text-[#292D32]" size={20} />
                        <p className="font-semibold text-sm">
                            {currentlyOpen
                                ? `Now Open until ${formatTime(restaurantData?.pickupEndTime)}`
                                : `Closed - Opens ${formatTime(restaurantData?.pickupStartTime)}`
                            }
                        </p>
                    </div>
                    <div className="text-[#19191C] text-sm">
                        {restaurantData?.delivery === "true" && restaurantData?.deliveryStartTime && restaurantData?.deliveryEndTime && (
                            <div>
                                <strong>Delivery Hours:</strong><br />
                                {formatTime(restaurantData?.deliveryStartTime)} - {formatTime(restaurantData?.deliveryEndTime)}
                            </div>
                        )}
                    </div>
                </div>

                {/* Address */}
                <div className="p-1 flex items-start space-x-2 mx-1.5 mt-2.5">
                    <div className="flex justify-center items-center space-x-1">
                        <MapPin className="text-[#292D32]" size={20} />
                        <div>
                            <p className="font-semibold text-sm">
                                {formatAddress()}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Map Container */}
                <div className="relative h-[220px] md:w-[678px] sm:w-12 justify-center m-auto rounded-lg overflow-hidden">
                    {isGeocoding ? (
                        <div className="flex items-center justify-center h-full bg-gray-100">
                            <div className="text-gray-600">Loading map...</div>
                        </div>
                    ) : (
                        <GoogleMap
                            mapContainerStyle={{ width: '100%', height: '100%' }}
                            center={mapCenter}
                            zoom={mapPosition ? 15 : 10}
                            options={{
                                zoomControl: true,
                                streetViewControl: false,
                                mapTypeControl: false,
                                fullscreenControl: false,
                            }}
                        >
                            {mapPosition && (
                                <Marker 
                                    position={mapPosition}
                                    title={restaurantData?.name}
                                />
                            )}
                        </GoogleMap>
                    )}
                </div>

                {/* Delivery Fee Section */}
                <div className="p-1 mx-1.5 mt-1.5">
                    <h3 className="font-bold text-[#19191C]">
                        Delivery Fee
                    </h3>
                    <p className="text-sm text-[#637381]">
                        {restaurantData?.delivery === "true"
                            ? formatDeliveryFee()
                            : "Delivery not available - Pickup only"
                        }
                    </p>
                </div>

                {/* Minimum Order Section */}
                <div className="p-1 mx-1.5 mb-4">
                    <h3 className="font-bold text-[#19191C]">Minimum Order</h3>
                    <p className="text-sm text-[#637381]">
                        Contact restaurant for minimum order information
                    </p>
                </div>
            </div>
        </div>
    );
};

export default InfoModal;