
/* eslint-disable no-useless-escape */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { FcGoogle } from 'react-icons/fc';
import { FaApple } from 'react-icons/fa';
import { Eye, EyeOff } from 'lucide-react';
import type { AppDispatch } from '../../redux-store/store';
import {
    registerCustomer,
    googleRegister,
    clearError,
    selectCustomerLoading,
    selectCustomerError,
    selectIsAuthenticated,
    appleLogin,
    selectAuthInitialized,
    initializeAuth
} from '../../redux-store/slices/customerSlice';
import { toast } from 'react-toastify';

// Extend the Window interface to include AppleID
declare global {
    interface Window {
        AppleID?: any;
        google?: any;
    }
}

// Social Login Manager Class
class SocialLoginManager {
  private state = {
    google: { loaded: false, loading: false, initialized: false },
    apple: { loaded: false, loading: false, initialized: false }
  };

  private subscribers: (() => void)[] = [];

  subscribe(callback: () => void) {
    this.subscribers.push(callback);
    return () => {
      this.subscribers = this.subscribers.filter(sub => sub !== callback);
    };
  }

  private notify() {
    this.subscribers.forEach(callback => callback());
  }

  updateState(provider: 'google' | 'apple', updates: Partial<typeof this.state.google>) {
    this.state[provider] = { ...this.state[provider], ...updates };
    this.notify();
  }

  getState() {
    return { ...this.state };
  }

  async loadGoogleScript(config: any, handleResponse: (response: any) => void): Promise<void> {
    if (this.state.google.loading) return;

    this.updateState('google', { loading: true });

    try {
      // Remove existing script
      const existingScript = document.querySelector('script[src*="accounts.google.com/gsi/client"]');
      if (existingScript) {
        existingScript.remove();
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;

      await new Promise<void>((resolve, reject) => {
        script.onload = () => {
          this.updateState('google', { loaded: true, loading: false });
          setTimeout(() => {
            this.initializeGoogle(config, handleResponse);
            resolve();
          }, 1000);
        };

        script.onerror = () => {
          this.updateState('google', { loading: false });
          reject(new Error('Failed to load Google script'));
        };

        document.head.appendChild(script);
      });
    } catch (error) {
      this.updateState('google', { loading: false });
      throw error;
    }
  }

  private initializeGoogle(config: any, handleResponse: (response: any) => void) {
    try {
      if (window.google?.accounts?.id) {
        window.google.accounts.id.initialize({
          client_id: config.GOOGLE_CLIENT_ID,
          callback: handleResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
          use_fedcm_for_prompt: false,
          prompt_parent_id: 'google-signin-button',
          context: 'signup',
          ux_mode: 'popup',
          state_cookie_domain: window.location.hostname
        });
        this.updateState('google', { initialized: true });
        console.log('✅ Google Sign-In initialized successfully');
      }
    } catch (error) {
      console.error('❌ Error initializing Google Sign-In:', error);
    }
  }

  async loadAppleScript(config: any): Promise<void> {
    if (this.state.apple.loading) return;

    this.updateState('apple', { loading: true });

    try {
      const existingScript = document.querySelector('script[src*="appleid.cdn-apple.com"]');
      if (existingScript) {
        existingScript.remove();
      }

      const script = document.createElement('script');
      script.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
      script.async = true;
      script.defer = true;

      await new Promise<void>((resolve, reject) => {
        script.onload = () => {
          this.updateState('apple', { loaded: true, loading: false });
          setTimeout(() => {
            this.initializeApple(config);
            resolve();
          }, 1000);
        };

        script.onerror = () => {
          this.updateState('apple', { loading: false });
          reject(new Error('Failed to load Apple script'));
        };

        document.head.appendChild(script);
      });
    } catch (error) {
      this.updateState('apple', { loading: false });
      throw error;
    }
  }

  private initializeApple(config: any) {
    try {
      if (window.AppleID?.auth) {
        window.AppleID.auth.init({
          clientId: config.APPLE_CLIENT_ID,
          scope: 'name email',
          redirectURI: config.REDIRECT_URI,
          usePopup: true,
          state: 'signup'
        });
        this.updateState('apple', { initialized: true });
        console.log('✅ Apple Sign-In initialized successfully');
      }
    } catch (error) {
      console.error('❌ Error initializing Apple Sign-In:', error);
    }
  }
}

interface SignupFormData {
    email: string;
    password: string;
    confirmPassword: string;
    referralCode?: string;
    firstName: string;
    lastName: string;
    phone: string;
    birthDate: string;
}

interface AppleSignInResponse {
    authorization: {
        code: string;
        id_token?: string;
    };
    user?: {
        name?: {
            firstName?: string;
            lastName?: string;
        };
        email?: string;
    };
}



// Enhanced Yup validation schema
const validationSchema = Yup.object({
    firstName: Yup.string()
        .trim()
        .min(2, 'First name must be at least 2 characters')
        .max(50, 'First name must be less than 50 characters')
        .matches(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
        .required('First name is required'),
    lastName: Yup.string()
        .trim()
        .min(2, 'Last name must be at least 2 characters')
        .max(50, 'Last name must be less than 50 characters')
        .matches(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
        .required('Last name is required'),
    email: Yup.string()
        .trim()
        .email('Please enter a valid email address')
        .max(320, 'Email address is too long')
        .required('Email is required'),
    phone: Yup.string()
        .trim()
        .matches(/^\+?[\d\s\-\(\)]{10,20}$/, 'Please enter a valid phone number (10-20 digits)')
        .nullable()
        .transform((value) => value === '' ? null : value),
    birthDate: Yup.date()
        .max(new Date(), 'Birth date cannot be in the future')
        .test('age', 'You must be at least 13 years old', function(value) {
            if (!value) return true; // Allow empty birth date
            const today = new Date();
            const birthDate = new Date(value);
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                return age - 1 >= 13;
            }
            return age >= 13;
        })
        .nullable()
        .transform((value) => value === '' ? null : value),
    password: Yup.string()
        .min(8, 'Password must be at least 8 characters long')
        .max(128, 'Password must be less than 128 characters')
        .matches(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#+\-_=])[A-Za-z\d@$!%*?&#+\-_=]*$/,
            'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
        )
        .required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password')], 'Passwords do not match')
        .required('Please confirm your password'),
    referralCode: Yup.string()
        .trim()
        .max(50, 'Referral code is too long')
        .nullable()
        .transform((value) => value === '' ? null : value)
});

const SignupPage: React.FC = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch<AppDispatch>();

    // Redux selectors
    const isLoading = useSelector(selectCustomerLoading);
    const error = useSelector(selectCustomerError);
    const isAuthenticated = useSelector(selectIsAuthenticated);
    const authInitialized = useSelector(selectAuthInitialized);

    // Local state
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    // Social login state
    const [socialManager] = useState(() => new SocialLoginManager());
    const [socialState, setSocialState] = useState(socialManager.getState());
    const [socialLoading, setSocialLoading] = useState({ google: false, apple: false });

    // Initial form values
    const initialValues: SignupFormData = {
        email: '',
        password: '',
        confirmPassword: '',
        referralCode: '',
        firstName: '',
        lastName: '',
        phone: '',
        birthDate: ''
    };

    // Configuration from environment variables with updated fallbacks
    const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || "122432034984-mc939due2sphq0lgslraliga9h36j8gf.apps.googleusercontent.com";
    const APPLE_CLIENT_ID = import.meta.env.VITE_APPLE_NAME || "patronpalLoginKey";
    const APPLE_KEY_ID = import.meta.env.VITE_APPLE_KEY_ID || "3NP7Y9SS6L";
    const REDIRECT_URI = `${window.location.origin}/auth/apple/callback`;

    const config = {
        GOOGLE_CLIENT_ID,
        APPLE_CLIENT_ID,
        APPLE_NAME: APPLE_CLIENT_ID,
        APPLE_KEY_ID,
        APPLE_SERVICES: import.meta.env.VITE_APPLE_SERVICES || "DeviceCheck,Account & Organizational Data Sharing",
        REDIRECT_URI
    };

    // Helper function to decode JWT token safely
    const parseJwtToken = useCallback((token: string) => {
        try {
            const base64Url = token.split('.')[1];
            if (!base64Url) throw new Error('Invalid token format');
            
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(
                atob(base64)
                    .split('')
                    .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
                    .join('')
            );
            
            const parsed = JSON.parse(jsonPayload);
            
            // Validate required fields
            if (!parsed.email || !parsed.sub) {
                throw new Error('Missing required user information');
            }
            
            return parsed;
        } catch (error) {
            console.error('Error parsing JWT token:', error);
            return null;
        }
    }, []);

    // Enhanced Google response handler
    const handleGoogleResponse = useCallback(async (response: any) => {
        if (!response?.credential) {
            console.error('❌ No credential received from Google');
            toast.error('No credential received from Google');
            return;
        }

        setSocialLoading(prev => ({ ...prev, google: true }));

        try {
            const userInfo = parseJwtToken(response.credential);
            if (!userInfo) {
                throw new Error('Failed to parse Google user information');
            }

            console.log('🔄 Processing Google user registration...');

            const result = await dispatch(googleRegister({
                Email: userInfo.email,
                googleId: userInfo.sub,
                FirstName: userInfo.given_name || '',
                LastName: userInfo.family_name || '',
                profile_pic: userInfo.picture || ''
            }));

            if (googleRegister.fulfilled.match(result)) {
                toast.success('Google sign-up successful! Welcome to PatronPal!');
                setTimeout(() => navigate('/'), 100);
            } else if (googleRegister.rejected.match(result)) {
                const errorMessage = result.payload as string;
                console.error('❌ Google registration failed:', errorMessage);

                if (errorMessage?.toLowerCase().includes('already exists') ||
                    errorMessage?.toLowerCase().includes('already registered')) {
                    toast.info('Account already exists. Redirecting to login page.');
                    setTimeout(() => navigate('/login'), 1500);
                } else {
                    toast.error(errorMessage || 'Google sign-up failed. Please try again.');
                }
            }
        } catch (error) {
            console.error('❌ Google Sign-up error:', error);

            let errorMessage = 'An error occurred during Google Sign-up';
            if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error: Unable to connect to server. Please check your internet connection.';
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }

            toast.error(errorMessage);
        } finally {
            setSocialLoading(prev => ({ ...prev, google: false }));
        }
    }, [dispatch, navigate, parseJwtToken]);

    // Enhanced Apple response handler
    const handleAppleResponse = useCallback(async (response: AppleSignInResponse) => {
        setSocialLoading(prev => ({ ...prev, apple: true }));

        try {
            console.log('🔄 Apple response received');

            if (!response?.authorization?.code) {
                throw new Error('No authorization code received from Apple');
            }

            const userEmail = response.user?.email || '';
            const firstName = response.user?.name?.firstName || '';
            const lastName = response.user?.name?.lastName || '';

            const appleData = {
                Email: userEmail,
                appleId: response.authorization.code,
                customToken: response.authorization.id_token || response.authorization.code,
                keyId: APPLE_KEY_ID,
                firstName: firstName,
                lastName: lastName
            };

            console.log('🔄 Processing Apple user registration...');

            const result = await dispatch(appleLogin(appleData) as any);

            if (result.type.endsWith('/fulfilled')) {
                toast.success('Apple sign-up successful! Welcome to PatronPal!');
                setTimeout(() => navigate('/'), 100);
            } else if (result.type.endsWith('/rejected')) {
                const errorMessage = result.payload as string;
                console.error('❌ Apple registration failed:', errorMessage);

                if (errorMessage?.toLowerCase().includes('already exists') ||
                    errorMessage?.toLowerCase().includes('already registered')) {
                    toast.info('Account already exists. Redirecting to login page.');
                    setTimeout(() => navigate('/login'), 1500);
                } else {
                    toast.error(errorMessage || 'Apple sign-up failed. Please try again.');
                }
            }
        } catch (error) {
            console.error('❌ Apple Sign-up error:', error);

            let errorMessage = 'An error occurred during Apple Sign-up';
            if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error: Unable to connect to server. Please check your internet connection.';
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }

            toast.error(errorMessage);
        } finally {
            setSocialLoading(prev => ({ ...prev, apple: false }));
        }
    }, [dispatch, navigate, APPLE_KEY_ID]);

    // Initialize social logins
    const initializeSocialLogins = useCallback(async () => {
        try {
            const promises = [];

            if (!socialState.google.loaded && !socialState.google.loading) {
                promises.push(socialManager.loadGoogleScript(config, handleGoogleResponse));
            }

            if (!socialState.apple.loaded && !socialState.apple.loading) {
                promises.push(socialManager.loadAppleScript(config));
            }

            if (promises.length > 0) {
                await Promise.allSettled(promises);
            }
        } catch (error) {
            console.error('❌ Error initializing social logins:', error);
        }
    }, [socialState, config, handleGoogleResponse, socialManager]);

    // Effects
    useEffect(() => {
        dispatch(initializeAuth());
    }, [dispatch]);

    // Subscribe to social login state changes
    useEffect(() => {
        const unsubscribe = socialManager.subscribe(() => {
            setSocialState(socialManager.getState());
        });

        return unsubscribe;
    }, [socialManager]);

    // Initialize social logins only when needed
    useEffect(() => {
        if (authInitialized && !isAuthenticated) {
            initializeSocialLogins();
        }
    }, [authInitialized, isAuthenticated, initializeSocialLogins]);

    // Redirect if already authenticated
    useEffect(() => {
        if (isAuthenticated) {
            navigate('/');
        }
    }, [isAuthenticated, navigate]);

    // Clear error when component unmounts
    useEffect(() => {
        return () => {
            dispatch(clearError());
        };
    }, [dispatch]);

    // Toggle functions
    const togglePasswordVisibility = useCallback(() => {
        setShowPassword(prev => !prev);
    }, []);

    const toggleConfirmPasswordVisibility = useCallback(() => {
        setShowConfirmPassword(prev => !prev);
    }, []);

    // Enhanced form submission
    const handleSubmit = useCallback(async (values: SignupFormData, { setSubmitting }: any) => {
        dispatch(clearError());

        try {
            // Trim and clean data
            const cleanedData = {
                Email: values.email.trim().toLowerCase(),
                Password: values.password,
                ConfirmPassword: values.confirmPassword,
                FirstName: values.firstName.trim(),
                LastName: values.lastName.trim(),
                Phone: values.phone.trim() || undefined,
                birthDate: values.birthDate || undefined,
                referralCode: values.referralCode?.trim() || undefined
            };

            const result = await dispatch(registerCustomer(cleanedData));

            if (registerCustomer.fulfilled.match(result)) {
                toast.success('Account created successfully! Please check your email for verification.');
                setTimeout(() => navigate('/login'), 2000);
            } else if (registerCustomer.rejected.match(result)) {
                const errorMessage = result.payload as string;
                toast.error(errorMessage || 'Registration failed. Please try again.');
            }
        } catch (error) {
            console.error('Registration error:', error);
            toast.error('An unexpected error occurred. Please try again.');
        } finally {
            setSubmitting(false);
        }
    }, [dispatch, navigate]);

    // Enhanced Google Sign-In handler
    const handleGoogleSignIn = useCallback(() => {
        if (!socialState.google.initialized) {
            toast.error('Google Sign-In is still loading. Please wait a moment and try again.');
            return;
        }

        if (socialLoading.google) {
            return; // Prevent multiple clicks
        }

        if (window.google?.accounts?.id) {
            try {
                console.log('🔄 Triggering Google Sign-In...');

                // Force account selection and disable auto-select
                window.google.accounts.id.disableAutoSelect();

                // Use prompt with specific options to force account selection
                window.google.accounts.id.prompt((notification: any) => {
                    if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                        console.log('Google Sign-In prompt not displayed, trying alternative method');

                        // Alternative: Open Google OAuth directly in popup
                        const authUrl = `https://accounts.google.com/oauth/authorize?` +
                            `client_id=${config.GOOGLE_CLIENT_ID}&` +
                            `redirect_uri=${encodeURIComponent(window.location.origin)}&` +
                            `response_type=code&` +
                            `scope=email profile&` +
                            `prompt=select_account&` +
                            `access_type=offline`;

                        const popup = window.open(authUrl, 'google-signin', 'width=500,height=600');

                        if (!popup) {
                            toast.error('Please allow popups for Google Sign-In');
                        }
                    }
                });

                // Force prompt to show account selection
                setTimeout(() => {
                    if (window.google?.accounts?.id) {
                        window.google.accounts.id.prompt();
                    }
                }, 100);

            } catch (error) {
                console.error('❌ Error triggering Google Sign-In:', error);
                toast.error('Failed to open Google Sign-In. Please try again.');
            }
        } else {
            console.error('❌ Google Sign-In not loaded');
            toast.error('Google Sign-In is not available. Please refresh the page and try again.');
        }
    }, [socialState.google.initialized, socialLoading.google, config.GOOGLE_CLIENT_ID]);

    // Enhanced Apple Sign-In handler
    const handleAppleSignIn = useCallback(async () => {
        if (socialLoading.apple || isLoading) {
            toast.warning('Please wait for the current operation to complete.');
            return;
        }

        if (!socialState.apple.initialized) {
            toast.error('Apple Sign-In is still loading. Please wait and try again.');
            return;
        }

        if (!window.AppleID?.auth) {
            toast.error('Apple Sign-In is not available. Please refresh the page.');
            return;
        }

        try {
            console.log('🔄 Attempting Apple Sign-In...');
            const response = await window.AppleID.auth.signIn({
                usePopup: true,
                state: JSON.stringify({
                    keyId: config.APPLE_KEY_ID,
                    name: config.APPLE_NAME,
                    services: config.APPLE_SERVICES,
                    timestamp: Date.now()
                })
            });
            console.log('✅ Apple Sign-In response received');
            await handleAppleResponse(response);
        } catch (error: any) {
            console.error('❌ Error with Apple Sign-In:', error);

            // Don't show error for user-cancelled actions
            const errorString = error?.toString() || '';
            if (error !== 'popup_closed_by_user' &&
                !errorString.includes('popup_closed_by_user') &&
                !errorString.includes('user_cancelled_authorize')) {
                toast.error('Failed to sign in with Apple. Please try again.');
            }
        }
    }, [socialLoading.apple, isLoading, socialState.apple.initialized, handleAppleResponse, config]);

    // Helper to disable form fields/buttons during loading or submitting
    const isSocialLoading = socialLoading.google || socialLoading.apple;
    const isFormDisabled = isLoading || isSocialLoading;

    return (
        <div className="flex justify-center items-center min-h-screen bg-gray-50 p-5">
            <div className="w-full max-w-md">
                <div className="text-left mb-8">
                    <h1 className="text-2xl font-bold text-gray-900">
                        Patron<span className='text-[#FF5C00]'>Pal</span>
                    </h1>
                    <h2 className="text-xl font-semibold mt-4 mb-2">Create your Account</h2>
                    <p className="text-gray-600 text-sm">
                        Join PatronPal to manage your customer experience.
                    </p>
                </div>

                {/* Display error message if there's an error */}
                {error && (
                    <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
                        {error}
                    </div>
                )}

                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    validateOnBlur={true}
                    validateOnChange={false}
                >
                    {({ values, isValid, isSubmitting, errors, touched }) => (
                        <Form className="space-y-4" noValidate>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                                        First Name *
                                    </label>
                                    <Field
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        autoComplete="given-name"
                                        placeholder="John"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                            errors.firstName && touched.firstName ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <ErrorMessage name="firstName" component="div" className="text-red-600 text-xs mt-1" />
                                </div>
                                <div>
                                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                                        Last Name *
                                    </label>
                                    <Field
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        autoComplete="family-name"
                                        placeholder="Doe"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                            errors.lastName && touched.lastName ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <ErrorMessage name="lastName" component="div" className="text-red-600 text-xs mt-1" />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                    Email *
                                </label>
                                <Field
                                    type="email"
                                    id="email"
                                    name="email"
                                    autoComplete="email"
                                    placeholder="<EMAIL>"
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.email && touched.email ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="email" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                                    Phone Number
                                </label>
                                <Field
                                    type="tel"
                                    id="phone"
                                    name="phone"
                                    autoComplete="tel"
                                    placeholder="(*************"
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.phone && touched.phone ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="phone" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700 mb-1">
                                    Date of Birth
                                </label>
                                <Field
                                    type="date"
                                    id="birthDate"
                                    name="birthDate"
                                    max={new Date().toISOString().split('T')[0]}
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.birthDate && touched.birthDate ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="birthDate" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                    Password *
                                </label>
                                <div className="relative">
                                    <Field
                                        type={showPassword ? 'text' : 'password'}
                                        id="password"
                                        name="password"
                                        autoComplete="new-password"
                                        placeholder="Create a strong password"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] pr-10 transition-colors ${
                                            errors.password && touched.password ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        onClick={togglePasswordVisibility}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                                        disabled={isLoading || isSubmitting}
                                    >
                                        {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                    </button>
                                </div>
                                <ErrorMessage name="password" component="div" className="text-red-600 text-xs mt-1" />
                                <p className="mt-1 text-xs text-gray-500">
                                    Must contain uppercase, lowercase, number, and special character
                                </p>
                            </div>

                            <div>
                                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                                    Confirm Password *
                                </label>
                                <div className="relative">
                                    <Field
                                        type={showConfirmPassword ? 'text' : 'password'}
                                        id="confirmPassword"
                                        name="confirmPassword"
                                        autoComplete="new-password"
                                        placeholder="Confirm your password"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] pr-10 transition-colors ${
                                            errors.confirmPassword && touched.confirmPassword 
                                                ? 'border-red-300 focus:ring-red-500' 
                                                : 'border-gray-300'
                                        }`}
                                        disabled={isFormDisabled}
                                    />
                                    <button
                                        type="button"
                                        onClick={toggleConfirmPasswordVisibility}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                                        disabled={isFormDisabled}
                                    >
                                        {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                    </button>
                                </div>
                                <ErrorMessage name="confirmPassword" component="div" className="text-red-600 text-xs mt-1" />
                                {values.password && values.confirmPassword && values.password !== values.confirmPassword && (
                                    <p className="mt-1 text-sm text-red-600 flex items-center">
                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                        </svg>
                                        Passwords do not match
                                    </p>
                                )}
                                {values.password && values.confirmPassword && values.password === values.confirmPassword && (
                                    <p className="mt-1 text-sm text-green-600 flex items-center">
                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        Passwords match
                                    </p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="referralCode" className="block text-sm font-medium text-gray-700 mb-1">
                                    Referral Code <span className="text-gray-400">(Optional)</span>
                                </label>
                                <Field
                                    type="text"
                                    id="referralCode"
                                    name="referralCode"
                                    placeholder="Enter referral code if you have one"
                                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors"
                                    disabled={isFormDisabled}
                                />
                                <ErrorMessage name="referralCode" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <button
                                type="submit"
                                disabled={isSubmitting || !isValid}
                                className="w-full bg-[#FF5C00] text-white py-2.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#FF5C00] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium hover:bg-[#e54f00] hover:shadow-md"
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Creating Account...
                                    </span>
                                ) : (
                                    'Create Account'
                                )}
                            </button>
                        </Form>
                    )}
                </Formik>

                <div className="mt-6 text-center">
                    <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                            <div className="w-full border-t border-gray-300" />
                        </div>
                        <div className="relative flex justify-center text-sm">
                            <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                        </div>
                    </div>
                </div>

                {/* Loading Overlay */}
                {isSocialLoading && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-8 max-w-sm w-full mx-4 text-center shadow-2xl">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF5C00] mx-auto mb-4"></div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                {socialLoading.google ? 'Signing up with Google' : 'Signing up with Apple'}
                            </h3>
                            <p className="text-gray-600 text-sm">
                                Please wait while we process your authentication...
                            </p>
                        </div>
                    </div>
                )}

                {/* Social Login Buttons */}
                <div className="mt-4 space-y-3">
                    <button
                        id="google-signin-button"
                        onClick={handleGoogleSignIn}
                        disabled={isLoading || socialLoading.google || !socialState.google || isSocialLoading}
                        className="flex items-center justify-center w-full px-4 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5C00] disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
                    >
                        {socialLoading.google ? (
                            <svg className="animate-spin w-5 h-5 mr-2 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        ) : (
                            <FcGoogle className="w-5 h-5 mr-2" />
                        )}
                        {socialLoading.google ? 'Signing up...' : !socialState.google ? 'Loading Google...' : 'Continue with Google'}
                    </button>

                    <button
                        onClick={handleAppleSignIn}
                        disabled={isLoading || socialLoading.apple || !socialState.apple || isSocialLoading}
                        className="flex items-center justify-center w-full px-4 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5C00] disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
                    >
                        <FaApple className="w-5 h-5 mr-2" />
                        {!socialState.apple ? 'Loading Apple...' : 'Continue with Apple'}
                    </button>
                </div>

                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-600">
                        Already have an account?{' '}
                        <Link to="/login" className="text-[#FF5C00] hover:text-[#e54f00] font-medium transition-colors">
                            Sign in here
                        </Link>
                    </p>
                </div>

                {/* Terms and Privacy */}
                <div className="mt-4 text-center">
                    <p className="text-xs text-gray-500">
                        By creating an account, you agree to our{' '}
                        <Link to="/terms" className="text-[#FF5C00] hover:underline">Terms of Service</Link>
                        {' '}and{' '}
                        <Link to="/privacy" className="text-[#FF5C00] hover:underline">Privacy Policy</Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default SignupPage;