import { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { FiChevronDown } from "react-icons/fi";
import { assets } from "../assets/assets";
import { useAppSelector, useAppDispatch } from "../redux-store/hooks";
import {
  selectFormattedAddress,
  selectFullAddressData,
  selectDeliveryOption,
  setDeliveryOption,
  type DeliveryOption,
  type AddressData,
  setFullAddressData,
  setFormattedAddress
} from "../redux-store/slices/addressSlice";
import { logout } from "../redux-store/slices/customerSlice";
import Footer from "../components/Footer";
import AddressDropdown from "../components/AddressDropdown";

interface CityData {
  name: string;
  image: string;
}

const Home = () => {
  const address = useAppSelector(selectFormattedAddress);
  const fullAddressData = useAppSelector(selectFullAddressData);
  const selectedOption = useAppSelector(selectDeliveryOption);
  const dispatch = useAppDispatch();

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const userId = useAppSelector((state) => state.customer?.currentCustomer?._id);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Check for saved address in localStorage
  useEffect(() => {
    const savedAddress = localStorage.getItem('lastSelectedAddress');
    if (savedAddress) {
      try {
        const addressData = JSON.parse(savedAddress) as AddressData;
        dispatch(setFullAddressData(addressData));

        // Format the address for display
        const formattedAddress = [
          addressData.City,
          addressData.State,
          addressData.Country
        ].filter(Boolean).join(', ');

        // Changed from setAddress to dispatch(setFormattedAddress)
        dispatch(setFormattedAddress(formattedAddress));
      } catch (error) {
        console.error('Error parsing saved address:', error);
      }
    }
  }, [dispatch]);

  const handleSelect = (option: DeliveryOption) => {
    dispatch(setDeliveryOption(option));
    setIsOpen(false);
  };

  // Handle address selection from dropdown
  const handleSelectAddress = (addressData: AddressData) => {
    dispatch(setFullAddressData(addressData));

    // Format the address for display in the input field and for filtering
    const formattedAddress = [
      addressData.City,
      addressData.State,
      addressData.Country
    ].filter(Boolean).join(', ');

    dispatch(setFormattedAddress(formattedAddress));
  };

  // Handle Find Food button click
  const handleFindFood = () => {
    if (!address.trim()) {
      // Don't navigate if no address is selected
      return;
    }

    // Navigate to restaurant list with the selected option and address as query parameters
    const addressParam = encodeURIComponent(address);

    // Include the full address data as a JSON string in localStorage for potential future use
    if (fullAddressData) {
      localStorage.setItem('lastSelectedAddress', JSON.stringify(fullAddressData));
    }

    // Save delivery option to localStorage
    localStorage.setItem('deliveryOption', selectedOption);

    navigate(`/all-restaurants?type=${selectedOption.toLowerCase()}&address=${addressParam}`);
  };

  const handleLogout = () => {
    // Dispatch logout action which will now clear cart and address data
    dispatch(logout());
    
    // Navigate to login page
    navigate("/login");
  };

  // Handle Find Food by City
  const handleFindFoodbyCity = (cityName: string) => () => {
    // Optionally, you can set the address and fullAddressData here if needed
    const addressParam = encodeURIComponent(cityName);
    dispatch(setFormattedAddress(addressParam));
    navigate(`/restaurant-list?type=${selectedOption.toLowerCase()}&address=${addressParam}`);
    localStorage.setItem('lastSelectedAddress', addressParam);
  };

  // City data
  const cities: CityData[] = [
    {
      name: "Swabi",
      image:
        "https://images.unsplash.com/photo-1534190760961-74e8c1c5c3da?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "New York",
      image:
        "https://images.unsplash.com/photo-1522083165195-3424ed129620?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Texas",
      image:
        "https://images.unsplash.com/photo-1601918774946-25832a4be0d6?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Chicago",
      image: `${assets.Chicago_image}`,
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* ----Header---- */}

      <header className="absolute w-full z-20 p-4 md:px-16 px-4 flex justify-between items-center">
        <div className="text-white font-bold text-xl">
          Patron<span className="text-primary">Pal</span>
        </div>
        {!userId ?
          <div className="flex gap-2">
            <Link to={'/login'} className="bg-primary text-white px-6 py-2 rounded-full">
              Log In
            </Link>
            <Link to={'/signup'} className="bg-transparent text-white border border-white px-6 py-2 rounded-full">
              Sign Up
            </Link>
          </div>
          : <button onClick={handleLogout} className="bg-primary cursor-pointer text-white px-6 py-2 rounded-full">
            Logout
          </button>
        }

      </header>
      <div className="flex-grow">
        {/* ---Hero Section--- */}
        <section className="relative md:h-screen h-[300px]">
          <div className="absolute inset-0 bg-gradient-to-r rounded-b-xl from-black to-transparent z-0"></div>
          <img
            src={assets.banner_one}
            alt="Delicious burger"
            className="absolute inset-0 w-full rounded-b-xl h-full object-cover object-bottom opacity-80"
          />
          <div className="relative md:mt-0 -mt-4 z-10 h-full flex flex-col justify-center px-4 md:px-16 md:max-w-2xl">
            <h1 className="text-white text-2xl md:text-3xl font-[600] mb-0">
              Cravings met, cupboards stocked
              <br />
              Without the trip!
            </h1>
            <div className="absolute md:top-[60%] top-[76%] left-0 right-0 px-4 md:px-16 w-full">
              <div className="flex items-center flex-col sm:flex-row gap-2 mt-0 w-full">
                <div className="relative flex-grow w-full bg-white rounded-md ">
                  <AddressDropdown
                    value={address}
                    onSelectAddress={handleSelectAddress}
                    autoFocus={false}
                  />
                </div>
                <div className="relative w-full md:max-w-[200px]" ref={dropdownRef}>
                  <button
                    onClick={() => setIsOpen((prev) => !prev)}
                    className="bg-white shadow shadow-gray-300 text-black px-6 py-2 md:py-3 rounded-md flex items-center justify-between w-full"
                  >
                    {selectedOption}
                    <FiChevronDown className="ml-2" />
                  </button>

                  {isOpen && (
                    <ul className="absolute z-10 bg-white shadow-md rounded-md mt-2 w-full">
                      {['Delivery', 'Pickup'].map((option) => (
                        <li
                          key={option}
                          onClick={() => handleSelect(option as DeliveryOption)}
                          className={`px-4 py-2 hover:bg-gray-100 cursor-pointer ${selectedOption === option ? 'font-semibold bg-gray-100' : ''
                            }`}
                        >
                          {option}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                <button
                  onClick={handleFindFood}
                  disabled={address.trim() === ''}
                  className={`${address.trim() === '' ? 'opacity-50 cursor-not-allowed' : ''} bg-primary text-white px-6 md:py-3 py-2 rounded-full whitespace-nowrap cursor-pointer md:w-fit w-full`}
                >
                  Find Food
                </button>
              </div>
            </div>
          </div>
        </section>
        {/* --Business Registration Section--- */}
        <div className="relative md:mt-0 mt-22">
          <section className=" relative flex flex-col md:flex-row bg-gray-100 items-center justify-between mt-5 rounded-lg">
            <img src={assets.banner_two} alt="Chef cooking" className="rounded-lg w-full h-80 relative object-cover" />
            <div className="absolute bg-white max-w-md m-6 md:p-6 p-2 rounded-xl z-10 shadow-lg md:ml-16 md:-translate-y-12 lg:translate-y-25 translate-y-56 pr-20 pb-4">
              <h2 className="font-bold text-lg mb-2">List your Business on <span className="text-primary">PatronPal</span></h2>
              <p className="mb-4 text-sm">Joining Patronpal is easy. We'll list your menu and products online, help you manage orders, and handle fast, reliable delivery — straight to hungry customers in no time.</p>
              <p className="text-sm mb-4">Ready to partner up? Let's get started today!</p>
              <Link to={'#'} className="bg-primary hover:bg-orange-600 text-white font-md  py-2.5 px-5 rounded-full">Get Started</Link>
            </div >
          </section>
        </div>
        {/*-- Popular Cities Section--- */}
        <section className="py-25 md:px-16 px-8 md:mt-0 mt-32 ">
          <div className="container mx-auto">
            <h2 className="text-2xl font-bold mb-8">
              Popular in following Cities and growing
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {cities
                .concat(cities, cities)
                .slice(0, 12)
                .map((city, index) => (
                  <div
                    onClick={handleFindFoodbyCity(city.name)}
                    key={index}
                    className="relative h-48 rounded-lg overflow-hidden cursor-pointer group"
                  >
                    <img
                      src={city.image}
                      alt={city.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"></div>
                    <div className="absolute top-4 left-4 text-white font-semibold text-lg">
                      {city.name}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </section>
      </div>
      <div>
        <Footer />
      </div>
    </div>
  );
};

export default Home;
