import React, { useRef } from 'react';
import { ChevronRight } from 'lucide-react';
import type { Restaurant } from '../../pages/Restaurant/types/types'; 
import RestaurantCard from '../RestaurentListbyFilterComponent/RestaurantCard';

interface HorizontalRestaurantListProps {
  restaurants: Restaurant[];
  title: string;
  userId?: string;
  onFavoriteToggle: (id: string) => void;
  favoriteLoading: boolean;
  maxItems?: number;
}

const HorizontalRestaurantList: React.FC<HorizontalRestaurantListProps> = ({
  restaurants,
  title,
  userId,
  onFavoriteToggle,
  favoriteLoading,
  maxItems = 10
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  const scrollSection = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = direction === 'right' ? 300 : -300;
      scrollRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  if (restaurants.length === 0) return null;

  return (
    <div className="mb-8">
      <div className="text-xl font-semibold mb-4">{title}</div>
      <div className="relative">
        <div
          ref={scrollRef}
          className="overflow-x-auto scrollbar-hide"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          <div className="flex space-x-4 pb-2 w-max">
            {restaurants.slice(0, maxItems).map(restaurant => (
              <div key={restaurant._id} className="w-48 flex-shrink-0">
                <RestaurantCard
                  restaurant={restaurant}
                  userId={userId}
                  onFavoriteToggle={onFavoriteToggle}
                  favoriteLoading={favoriteLoading}
                />
              </div>
            ))}
          </div>
        </div>
        <button
          className="absolute -right-2.5 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-sm shadow-gray-400 hover:bg-gray-100 z-10"
          onClick={() => scrollSection('right')}
        >
          <ChevronRight className="w-5 h-5 text-primary" />
        </button>
      </div>
    </div>
  );
};

export default HorizontalRestaurantList;
