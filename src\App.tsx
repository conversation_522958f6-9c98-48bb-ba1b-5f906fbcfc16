// App.js
import { Routes, Route, useLocation } from 'react-router-dom';
import { LoadScript } from '@react-google-maps/api';
import { PersistGate } from 'redux-persist/integration/react';
import { persistor } from './redux-store/store';

// Importing Pages
import LoginPage from './pages/Users/<USER>';
import Home from './pages/Home';
import ProfileSettings from './pages/profile/ProfileSettings';
import Footer from './components/Footer';
import Navbar from './components/Navbar';
import Checkout from './pages/Checkout';
import TrackingPage from './pages/Tracking/TrackingPage';
import LiveTrackingPage from './pages/Tracking/LiveTrackingPage';
import ProductPage from './pages/Product/ProductPage';
import RestaurantList from './pages/Restaurant/RestaurantList';
import { useDispatch } from 'react-redux';
import { initializeAuth } from './redux-store/slices/customerSlice';
import { initializeAddressState } from './redux-store/slices/addressSlice';
import { useEffect } from 'react';
import useTokenValidation from './hooks/useTokenValidation';
import ForgetPassword from './pages/Users/<USER>';
import ResetPassword from './pages/Users/<USER>';
import SignupPage from './pages/Users/<USER>';
import RestaurantListbyFilters from './pages/Restaurant/RestaurantListbyFilters';
import FavoritesPage from './components/FavoritesPage';
import CartPage from './pages/CartPage';
import ContactUs from './pages/ContactUs';
import ProtectedRoute from './utils/ProtectedRoute';


// Wrapper component to use hooks like useLocation outside of Router
const Layout = () => {
  const dispatch = useDispatch();

  useTokenValidation();

  // Initialize authentication state from localStorage on app start
  useEffect(() => {
    dispatch(initializeAuth());
    dispatch(initializeAddressState());
  }, [dispatch]);

  const location = useLocation();

  // Define paths where you don't want the Footer or Navbar
  const hideFooterAndNavbar = ['/login', '/'];

  const shouldHide = hideFooterAndNavbar.includes(location.pathname);

  return (
    <PersistGate loading={null} persistor={persistor}>
      <div className="App">
        {!shouldHide && <Navbar />}
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          <Route path='/forget-password' element={<ForgetPassword />} />
          <Route path='/reset-password' element={<ResetPassword />} />
          <Route path="/" element={<Home />} />
          <Route path="/checkout" element={<ProtectedRoute><Checkout /></ProtectedRoute>} />
          <Route path="/tracking/" element={<TrackingPage />} />
          <Route path="/live-tracking" element={<LiveTrackingPage />} />
          <Route path="/profile-settings" element={<ProfileSettings />} />
          <Route path="/all-restaurants" element={<RestaurantListbyFilters />} />
          <Route path="/product/:id/:userid" element={<ProductPage />} />
          <Route path='/restaurant-list' element={<RestaurantList />} />
          <Route path='/favorites' element={<ProtectedRoute><FavoritesPage /></ProtectedRoute>} />
          <Route path='cart' element={<CartPage userId={''} />} />
          <Route path='/contact' element={<ContactUs />} />
        </Routes>
        {!shouldHide && <Footer />}
      </div>
    </PersistGate>
  );
};

const App = () => {
  return (
    <LoadScript
      googleMapsApiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}
      loadingElement={<div></div>}
    >
      <Layout />
    </LoadScript>
  );
};

export default App;