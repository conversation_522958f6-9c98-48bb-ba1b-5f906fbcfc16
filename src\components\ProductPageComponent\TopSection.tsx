import { Eye, Heart, Info, MapPin } from "lucide-react";
import { useEffect } from "react";


type GalleryMediaItem = {
    src: string;
    // add other fields if needed
};

interface TopSectionProps {
    restaurantData: {
        _id: string;
        userId: any;
        Line2: any;
        Line1: any;
        City: any;
        Country: any;
        image: string;
        name: string;
        description: string;
        address: string;
        rating: number;
        reviewCount: number;
        reviews?: any[];
        hasReviews?: boolean;
        favorites?: Array<{
            _id?: string;
            customerId: any;
        }>;
    };
    setIsReviewModalOpen: (open: boolean) => void;
    setIsInfoModalOpen: (open: boolean) => void;
    galleryMediaItems: GalleryMediaItem[];
    onFavoriteToggle?: (restaurantId: string) => void;
    setCurrentGalleryIndex: (idx: number) => void;
    userId?: string;
    setIsGalleryModalOpen: (open: boolean) => void;
}

function TopSection({
    restaurantData,
    setIsReviewModalOpen,
    setIsInfoModalOpen,
    galleryMediaItems,
    userId = '',
    setCurrentGalleryIndex,
    onFavoriteToggle,
    setIsGalleryModalOpen
}: TopSectionProps) {


    const formatAddress = () => {
        const parts = [restaurantData.Country, restaurantData.Line1, restaurantData.Line2, restaurantData.City];
        const filteredParts = parts.filter(
            part =>
                part &&
                part.trim().toLowerCase() !== 'undefined' &&
                part.trim() !== ''
        );
        return filteredParts.join(', ');
    };

    const isRestaurantFavorited = () => {
        console.log('[TopSection] Checking if restaurant is favorited');
        console.log('[TopSection] Restaurant favorites:', restaurantData.favorites);
        console.log('[TopSection] User ID:', userId);
        
        if (!restaurantData.favorites || !userId) {
            console.log('[TopSection] No favorites or userId, returning false');
            return false;
        }
        
        const result = restaurantData.favorites.some(fav => {
            const favCustomerId = typeof fav.customerId === 'object' ? fav.customerId?._id : fav.customerId;
            const isMatch = favCustomerId === userId;
            console.log('[TopSection] Favorite check:', { favCustomerId, userId, isMatch });
            return isMatch;
        });
        
        console.log('[TopSection] Is favorited result:', result);
        return result;
    };

    const isFavorited = isRestaurantFavorited();
    useEffect(() => {
        console.log('[TopSection] isFavorited state:', isFavorited);
    }, [isFavorited]);

    const handleFavoriteClick = (e: React.MouseEvent) => {
        console.log('[TopSection] Favorite button clicked');
        e.stopPropagation();
        e.preventDefault();
        if (onFavoriteToggle) {
            console.log('[TopSection] Calling onFavoriteToggle with:', restaurantData._id);
            onFavoriteToggle(restaurantData._id);
        } else {
            console.log('[TopSection] onFavoriteToggle is not defined');
        }
    };

    useEffect(() => {
        console.log('[TopSection] Component rendered with props:', {
            restaurantId: restaurantData._id,
            userId,
            hasFavorites: !!restaurantData.favorites,
            favoritesCount: restaurantData.favorites?.length
        });
    }, [restaurantData, userId]);

    return (
        <>
            <div className="bg-white m-1.5 p-2 rounded-lg">
                <div className="flex flex-col md:flex-row gap-6 mb-6">
                    <div className="w-full md:w-1/3">
                        <img
                            src={restaurantData.image || 'https://patronpal.com/assets/aaa.jpeg'}
                            alt={restaurantData.name}
                            className="w-full h-auto rounded-lg shadow-md object-cover"
                        />
                    </div>
                    <div className="w-full md:w-2/3 relative">
                        <h1 className="text-2xl font-bold mb-2">{restaurantData.name}</h1>
                        <p className="text-gray-600 mb-2">
                            {restaurantData.description}
                        </p>

                        <div className="flex items-center gap-2 mb-2 relative">
                            <MapPin size={16} className="text-gray-500" />
                            <span className="text-gray-700">{formatAddress()}</span>
                            <button
                                onClick={handleFavoriteClick}
                                className=" bg-white p-2 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
                            >
                                <Heart
                                    className={`h-5 w-5 ${isFavorited ? 'text-orange-500 fill-current' : 'text-orange-500'}`}
                                />
                            </button>
                        </div>

                        <div className="flex items-center gap-2 mb-2">
                            <div className="flex items-center justify-center bg-green-100 rounded-full w-6 h-6">
                                <svg
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    className="w-4 h-4 text-green-600"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span>Delivery</span>
                        </div>
                        {restaurantData.hasReviews && (
                            <div className="flex items-center gap-2 mb-4">
                                <span className="text-yellow-500">★</span>
                                <span>{restaurantData.rating.toFixed(1)}/5</span>
                                <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">
                                    {restaurantData.reviewCount}+
                                </span>
                                <button
                                    onClick={() => setIsReviewModalOpen(true)}
                                    className="text-blue-500 text-sm cursor-pointer">See Reviews</button>
                            </div>
                        )}
                        <button onClick={() => setIsInfoModalOpen(true)} className="flex cursor-pointer items-center text-gray-600">
                            <Info size={16} className="mr-1" />
                            <span>More Info</span>
                        </button>
                    </div>
                </div>

                {/* Restaurant Media Gallery */}
                <div className="mb-8">
                    <h2 className="text-lg font-semibold mb-3">
                        Restaurant Media Gallery
                    </h2>
                    <div className="flex gap-3 overflow-x-auto pb-2">
                        {galleryMediaItems.map((img, idx) => (
                            <div
                                key={idx}
                                className="relative cursor-pointer w-24 h-24 rounded-lg overflow-hidden shadow-sm flex-shrink-0"
                                onClick={() => {
                                    setCurrentGalleryIndex(idx);
                                    setIsGalleryModalOpen(true);
                                }}
                            >
                                <img
                                    src={img.src}
                                    alt={`Gallery ${idx + 1}`}
                                    className="w-full h-full object-cover"
                                />
                                <div className="absolute inset-0 z-10 flex items-center justify-center bg-black opacity-60 pointer-events-none">
                                    <Eye size={20} className="text-white" />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Offers Available */}
                <div className="mb-8">
                    <h2 className="text-lg font-semibold mb-3">Offers Available</h2>
                    <div className="flex gap-4 overflow-x-auto pb-2" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                        <div className="flex-shrink-0 overflow-hidden flex items-center">
                            <div className="p-3.5 border border-gray-200 rounded-lg rounded-r-none">
                                <p className="whitespace-nowrap">
                                    Free Delivery on First Order
                                </p>
                            </div>
                            <div className="bg-[#FF7527] text-white p-3 flex items-center justify-center">
                                <span className="text-xl font-bold">50%</span>
                            </div>
                            <svg className="text-primary -ml-1" width="10" height="50" viewBox="0 0 8 51" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 0C4 1.60489 5.50703 2.96046 7.57339 3.39541C7.81671 3.44663 8 3.65542 8 3.90407V6.29586C8 6.54451 7.81671 6.7533 7.57339 6.80452C5.50703 7.23947 4 8.59504 4 10.1999C4 11.8048 5.50703 13.1604 7.57339 13.5953C7.81671 13.6466 8 13.8553 8 14.104V16.4958C8 16.7444 7.81671 16.9532 7.57339 17.0044C5.50703 17.4394 4 18.795 4 20.3999C4 22.0048 5.50703 23.3603 7.57339 23.7953C7.81671 23.8465 8 24.0553 8 24.3039V26.6961C8 26.9447 7.81671 27.1535 7.57339 27.2047C5.50703 27.6397 4 28.9952 4 30.6001C4 32.205 5.50703 33.5606 7.57339 33.9956C7.81671 34.0468 8 34.2556 8 34.5042V36.896C8 37.1446 7.81671 37.3534 7.57339 37.4047C5.50703 37.8396 4 39.1952 4 40.8001C4 42.405 5.50703 43.7605 7.57339 44.1955C7.81671 44.2467 8 44.4555 8 44.7041V47.0959C8 47.3446 7.81671 47.5534 7.57339 47.6046C5.50703 48.0395 4 49.3951 4 51H0V0H4Z" fill="#FF7527" />
                            </svg>
                        </div>

                        <div className="flex-shrink-0  overflow-hidden flex items-center">
                            <div className="p-3.5 border border-gray-200 rounded-lg rounded-r-none">
                                <p className="whitespace-nowrap">Buy one Get One free</p>
                            </div>
                            <div className="bg-[#FF7527] text-white p-3 flex items-center justify-center">
                                <span className="text-xl font-bold">50%</span>
                            </div>
                            <svg className="text-primary -ml-1" width="10" height="50" viewBox="0 0 8 51" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 0C4 1.60489 5.50703 2.96046 7.57339 3.39541C7.81671 3.44663 8 3.65542 8 3.90407V6.29586C8 6.54451 7.81671 6.7533 7.57339 6.80452C5.50703 7.23947 4 8.59504 4 10.1999C4 11.8048 5.50703 13.1604 7.57339 13.5953C7.81671 13.6466 8 13.8553 8 14.104V16.4958C8 16.7444 7.81671 16.9532 7.57339 17.0044C5.50703 17.4394 4 18.795 4 20.3999C4 22.0048 5.50703 23.3603 7.57339 23.7953C7.81671 23.8465 8 24.0553 8 24.3039V26.6961C8 26.9447 7.81671 27.1535 7.57339 27.2047C5.50703 27.6397 4 28.9952 4 30.6001C4 32.205 5.50703 33.5606 7.57339 33.9956C7.81671 34.0468 8 34.2556 8 34.5042V36.896C8 37.1446 7.81671 37.3534 7.57339 37.4047C5.50703 37.8396 4 39.1952 4 40.8001C4 42.405 5.50703 43.7605 7.57339 44.1955C7.81671 44.2467 8 44.4555 8 44.7041V47.0959C8 47.3446 7.81671 47.5534 7.57339 47.6046C5.50703 48.0395 4 49.3951 4 51H0V0H4Z" fill="#FF7527" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}

export default TopSection
