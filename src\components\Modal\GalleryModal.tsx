import { useEffect, useState } from "react";
import { ChevronLeft, ChevronRight, X } from "lucide-react";

// Define types for media items
type MediaType = "image" | "video";

interface GalleryItem {
    src: string;
    type: MediaType;
    thumbnail?: string;
}

// Props interface for the GalleryModal component
interface GalleryModalProps {
    isOpen: boolean;
    onClose: () => void;
    images: (GalleryItem | string)[];
    initialIndex?: number;
}

const GalleryModal: React.FC<GalleryModalProps> = ({
    isOpen,
    onClose,
    images,
    initialIndex = 0
}) => {
    const [currentIndex, setCurrentIndex] = useState<number>(initialIndex);
    const [isPlaying, setIsPlaying] = useState<boolean>(false);

    // Close modal when Escape key is pressed
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent): void => {
            if (e.key === "Escape") onClose();
        };

        if (isOpen) {
            document.addEventListener("keydown", handleEscape);
        }

        return () => {
            document.removeEventListener("keydown", handleEscape);
        };
    }, [isOpen, onClose]);

    // Prevent scrolling when modal is open
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = "hidden";
        } else {
            document.body.style.overflow = "auto";
        }

        return () => {
            document.body.style.overflow = "auto";
        };
    }, [isOpen]);

    if (!isOpen) return null;

    // Handle both string and GalleryItem types
    const getCurrentItem = (): GalleryItem => {
        const item = images[currentIndex];
        return typeof item === 'string'
            ? { src: item, type: 'image' }
            : item;
    };

    const currentItem = getCurrentItem();
    const isVideo = currentItem.type === "video";

    const goToPrevious = (e: React.MouseEvent): void => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
        setIsPlaying(false);
    };

    const goToNext = (e: React.MouseEvent): void => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
        setIsPlaying(false);
    };

    const togglePlay = (e: React.MouseEvent): void => {
        e.stopPropagation();
        setIsPlaying(!isPlaying);
    };

    // Function to get display src and thumbnail for any image item
    const getImageDetails = (item: GalleryItem | string, _index: number): { src: string, thumbnail: string } => {
        if (typeof item === 'string') {
            return { src: item, thumbnail: item };
        }
        return {
            src: item.src,
            thumbnail: item.thumbnail || item.src
        };
    };

    return (
        <div
            className="fixed inset-0 backdrop-blur flex items-center justify-center z-50 " style={{ backgroundColor: 'rgb(0 0 0 / 49%)' }}
            onClick={onClose}
        >
            {/* Modal Content */}
            <div
                className="relative w-full max-w-2xl mx-auto"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
            >
                <button onClick={onClose} className="pb-1 cursor-pointer w-full md:hidden flex justify-end">
                    <X size={28} className="" />
                </button>
                {/* Main content */}
                <div className="relative flex justify-center items-center rounded-xl" onClick={onClose}>
                    {/* Image/Video display */}
                    {isVideo ? (
                        isPlaying ? (
                            <video
                                src={currentItem.src}
                                className="w-[340px] md:h-[540px] h-[530px] object-cover rounded-xl"
                                autoPlay
                                controls
                            />
                        ) : (
                            <div className="relative flex justify-center items-center rounded-xl overflow-hidden">
                                <img
                                    src={currentItem.thumbnail || currentItem.src}
                                    alt="Video thumbnail"
                                    className="w-[340px] md:h-[540px] h-[530px] object-cover object-center rounded-xl"
                                />
                                <div
                                    className="absolute inset-0 flex items-center justify-center cursor-pointer"
                                    onClick={togglePlay}
                                >
                                    <svg className="z-50" width="41" height="43" viewBox="0 0 41 43" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5 21.0162V14.3362C5 6.0162 10.88 2.6162 18.08 6.7762L23.88 10.1362L29.68 13.4962C36.88 17.6562 36.88 24.4562 29.68 28.6162L23.88 31.9762L18.08 35.3362C10.88 39.4962 5 36.0962 5 27.7762V21.0162Z" stroke="white" stroke-width="10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div className={`absolute inset-0 bg-black/50 opacity-50
                                        `}></div>
                                </div>
                            </div>
                        )
                    ) : (
                        <img
                            src={currentItem.src}
                            alt="Gallery item"
                            className="w-[340px] md:h-[540px] h-[530px] object-cover rounded-xl"
                        />
                    )}

                    {/* Navigation arrows */}
                    {images.length > 1 && (
                        <>
                            <button
                                className="absolute md:left-18 left-1 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-70 rounded-full p-2"
                                onClick={goToPrevious}
                            >
                                <ChevronLeft size={24} className="text-primary" />
                            </button>
                            <button
                                className="absolute md:right-18 right-1 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-70 rounded-full p-2"
                                onClick={goToNext}
                            >
                                <ChevronRight size={24} className="text-primary" />
                            </button>
                        </>
                    )}
                </div>

                {/* Thumbnails */}
                <div className="flex justify-center gap-2 mt-4">
                    {images.map((image, index) => {
                        const { thumbnail } = getImageDetails(image, index);
                        return (
                            <div
                                key={index}
                                className={`w-16 relative h-16 cursor-pointer rounded-md overflow-hidden 
                                    `}
                                onClick={(e: React.MouseEvent) => {
                                    e.stopPropagation();
                                    setCurrentIndex(index);
                                    setIsPlaying(false);
                                }}
                            >
                                <img
                                    src={thumbnail}
                                    alt={`Thumbnail ${index}`}
                                    className="w-full h-full object-cover"
                                />

                                <div className={`absolute inset-0 bg-white/70 ${index === currentIndex ? 'opacity-0' : 'opacity-50'
                                    }`}></div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default GalleryModal;