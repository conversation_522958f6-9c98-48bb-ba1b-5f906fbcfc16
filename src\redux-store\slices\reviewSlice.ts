/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  createSlice,
  createAsyncThunk,
  type PayloadAction,
} from "@reduxjs/toolkit";
import apiClient from "../config";

// Review interface
interface Review {
  _id: string;
  customerId: string;
  restaurantId: string;
  rating: number;
  comment?: string;
  likes?: number;
  customerName?: string;
  restaurantName?: string;
  createdAt: string;
  updatedAt: string;
  isLiked?: boolean;
  __v?: number;
}

// Request interfaces
interface ReviewCreateRequest {
  customerId: string;
  restaurantId: string;
  rating: number;
  comment?: string;
  customerName?: string;
  restaurantName?: string;
}

interface ReviewUpdateRequest extends ReviewCreateRequest {
  _id: string;
  __v?: number;
}

interface ReviewFilters {
  customerId?: string;
  restaurantId?: string;
  rating?: number;
  limit?: number;
  page?: number;
}

interface SearchRequest {
  q: string;
  restaurantId?: string;
}

// State interface
interface ReviewState {
  reviews: Review[];
  currentReview: Review | null;
  filteredReviews: Review[];
  loading: boolean;
  error: string | null;
  searchResults: Review[];
  searchLoading: boolean;
  postingReview: boolean;
  updatingReview: boolean;
  deletingReview: boolean;
  likingReview: boolean;
  totalReviews: number;
  averageRating: number;
}

// Root state interface - add this to ensure type safety
interface RootState {
  review: ReviewState;
  // Add other slices here as needed
}

// Initial state
const initialState: ReviewState = {
  reviews: [],
  currentReview: null,
  filteredReviews: [],
  loading: false,
  error: null,
  searchResults: [],
  searchLoading: false,
  postingReview: false,
  updatingReview: false,
  deletingReview: false,
  likingReview: false,
  totalReviews: 0,
  averageRating: 0,
};

// Async thunks
export const getReviews = createAsyncThunk<
  { reviews: Review[]; total: number; average: number },
  ReviewFilters,
  { rejectValue: string }
>("review/getReviews", async (filters, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    if (filters.customerId) params.append("customerId", filters.customerId);
    if (filters.restaurantId) params.append("restaurantId", filters.restaurantId);
    if (filters.rating) params.append("rating", filters.rating.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());
    if (filters.page) params.append("page", filters.page.toString());

    const response = await apiClient.get<{
      reviews: Review[];
      total: number;
      average: number;
    }>(`/reviews?${params.toString()}`);

    console.log("[getReviews] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch reviews";
    return rejectWithValue(message);
  }
});

export const getReviewsByRestaurantDetailed = createAsyncThunk<
  { reviews: Review[]; total: number; average: number },
  string,
  { rejectValue: string }
>("review/getReviewsByRestaurant", async (restaurantId, { rejectWithValue }) => {
  try {
    console.log("[getReviewsByRestaurant] Fetching reviews for restaurant:", restaurantId);
    
    const response = await apiClient.get<{
      reviews: Review[];
      total: number;
      average: number;
    }>(`/reviews/${restaurantId}`);
    
    console.log("[getReviewsByRestaurant] API response:", response.data);
    
    // Ensure we have proper default values
    const result = {
      reviews: response.data.reviews || [],
      total: response.data.total || 0,
      average: response.data.average || 0
    };
    
    return result;
  } catch (error: any) {
    console.error("[getReviewsByRestaurant] Error:", error);
    const message = error.response?.data?.message || error.message || "Failed to fetch restaurant reviews";
    return rejectWithValue(message);
  }
});

export const getReviewsByCustomer = createAsyncThunk<
  Review[],
  string,
  { rejectValue: string }
>("review/getReviewsByCustomer", async (customerId, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Review[]>(`/reviews/customer/${customerId}`);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch customer reviews";
    return rejectWithValue(message);
  }
});

export const getReviewById = createAsyncThunk<
  Review,
  string,
  { rejectValue: string }
>("review/getReviewById", async (id, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Review>(`/review/${id}`);
    console.log("[getReviewById] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch review";
    return rejectWithValue(message);
  }
});

export const searchReviews = createAsyncThunk<
  Review[],
  SearchRequest,
  { rejectValue: string }
>("review/searchReviews", async ({ q, restaurantId }, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    params.append("q", q);
    if (restaurantId) params.append("restaurantId", restaurantId);

    const response = await apiClient.get<Review[]>(`/reviews/search?${params.toString()}`);
    console.log("[searchReviews] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to search reviews";
    return rejectWithValue(message);
  }
});

export const createReview = createAsyncThunk<
  Review,
  ReviewCreateRequest,
  { rejectValue: string }
>("review/createReview", async (reviewData, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<Review>("/review", reviewData, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to create review";
    return rejectWithValue(message);
  }
});

export const updateReview = createAsyncThunk<
  Review,
  { id: string; reviewData: ReviewUpdateRequest },
  { rejectValue: string }
>("review/updateReview", async ({ id, reviewData }, { rejectWithValue }) => {
  try {
    const response = await apiClient.put<{
      result: Review;
      message: string;
    }>(`/review/${id}`, reviewData, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data.result;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to update review";
    return rejectWithValue(message);
  }
});

export const deleteReview = createAsyncThunk<
  string,
  string,
  { rejectValue: string }
>("review/deleteReview", async (id, { rejectWithValue }) => {
  try {
    await apiClient.delete<{ message: string }>(`/review/${id}`);
    return id; // Return the deleted review ID
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to delete review";
    return rejectWithValue(message);
  }
});

export const likeReview = createAsyncThunk<
  Review,
  string,
  { rejectValue: string }
>("review/likeReview", async (reviewId, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<{
      updatedReview: Review;
      message: string;
    }>(`/review/${reviewId}/like`, {}, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data.updatedReview;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to like review";
    return rejectWithValue(message);
  }
});

export const unlikeReview = createAsyncThunk<
  Review,
  string,
  { rejectValue: string }
>("review/unlikeReview", async (reviewId, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<{
      updatedReview: Review;
      message: string;
    }>(`/review/${reviewId}/unlike`, {}, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data.updatedReview;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to unlike review";
    return rejectWithValue(message);
  }
});

// Review slice
const reviewSlice = createSlice({
  name: "review",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
      state.filteredReviews = [];
    },
    setCurrentReview: (state, action: PayloadAction<Review>) => {
      state.currentReview = action.payload;
    },
    clearCurrentReview: (state) => {
      state.currentReview = null;
    },
    setReviews: (state, action: PayloadAction<Review[]>) => {
      state.reviews = action.payload;
    },
    clearReviews: (state) => {
      state.reviews = [];
      state.filteredReviews = [];
      state.totalReviews = 0;
      state.averageRating = 0;
    },
    updateReviewInState: (state, action: PayloadAction<Review>) => {
      const index = state.reviews.findIndex(r => r._id === action.payload._id);
      if (index !== -1) {
        state.reviews[index] = action.payload;
      }
    },
    // Add a reset action to handle initialization issues
    resetReviewState: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    builder
      // Get reviews
      .addCase(getReviews.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getReviews.fulfilled, (state, action) => {
        state.loading = false;
        state.reviews = action.payload.reviews;
        state.totalReviews = action.payload.total;
        state.averageRating = action.payload.average;
      })
      .addCase(getReviews.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch reviews";
      })

      // Get reviews by restaurant
      .addCase(getReviewsByRestaurantDetailed.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getReviewsByRestaurantDetailed.fulfilled, (state, action) => {
        state.loading = false;
        state.reviews = action.payload.reviews;
        state.totalReviews = action.payload.total;
        state.averageRating = action.payload.average;
      })
      .addCase(getReviewsByRestaurantDetailed.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch restaurant reviews";
      })

      // Get reviews by customer
      .addCase(getReviewsByCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getReviewsByCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.filteredReviews = action.payload;
      })
      .addCase(getReviewsByCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch customer reviews";
      })

      // Get review by ID
      .addCase(getReviewById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getReviewById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentReview = action.payload;
      })
      .addCase(getReviewById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch review";
      })

      // Search reviews
      .addCase(searchReviews.pending, (state) => {
        state.searchLoading = true;
        state.error = null;
      })
      .addCase(searchReviews.fulfilled, (state, action) => {
        state.searchLoading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchReviews.rejected, (state, action) => {
        state.searchLoading = false;
        state.error = action.payload || "Failed to search reviews";
      })

      // Create review
      .addCase(createReview.pending, (state) => {
        state.postingReview = true;
        state.error = null;
      })
      .addCase(createReview.fulfilled, (state, action) => {
        state.postingReview = false;
        state.reviews.unshift(action.payload); // Add to beginning
        state.totalReviews += 1;
      })
      .addCase(createReview.rejected, (state, action) => {
        state.postingReview = false;
        state.error = action.payload || "Failed to create review";
      })

      // Update review
      .addCase(updateReview.pending, (state) => {
        state.updatingReview = true;
        state.error = null;
      })
      .addCase(updateReview.fulfilled, (state, action) => {
        state.updatingReview = false;
        const index = state.reviews.findIndex(
          (r) => r._id === action.payload._id
        );
        if (index !== -1) {
          state.reviews[index] = action.payload;
        }
        if (
          state.currentReview &&
          state.currentReview._id === action.payload._id
        ) {
          state.currentReview = action.payload;
        }
      })
      .addCase(updateReview.rejected, (state, action) => {
        state.updatingReview = false;
        state.error = action.payload || "Failed to update review";
      })

      // Delete review
      .addCase(deleteReview.pending, (state) => {
        state.deletingReview = true;
        state.error = null;
      })
      .addCase(deleteReview.fulfilled, (state, action) => {
        state.deletingReview = false;
        state.reviews = state.reviews.filter((r) => r._id !== action.payload);
        state.totalReviews = Math.max(0, state.totalReviews - 1);
        if (
          state.currentReview &&
          state.currentReview._id === action.payload
        ) {
          state.currentReview = null;
        }
      })
      .addCase(deleteReview.rejected, (state, action) => {
        state.deletingReview = false;
        state.error = action.payload || "Failed to delete review";
      })

      // Like review
      .addCase(likeReview.pending, (state) => {
        state.likingReview = true;
        state.error = null;
      })
      .addCase(likeReview.fulfilled, (state, action) => {
        state.likingReview = false;
        const index = state.reviews.findIndex(
          (r) => r._id === action.payload._id
        );
        if (index !== -1) {
          state.reviews[index] = action.payload;
        }
        if (
          state.currentReview &&
          state.currentReview._id === action.payload._id
        ) {
          state.currentReview = action.payload;
        }
      })
      .addCase(likeReview.rejected, (state, action) => {
        state.likingReview = false;
        state.error = action.payload || "Failed to like review";
      })

      // Unlike review
      .addCase(unlikeReview.pending, (state) => {
        state.likingReview = true;
        state.error = null;
      })
      .addCase(unlikeReview.fulfilled, (state, action) => {
        state.likingReview = false;
        const index = state.reviews.findIndex(
          (r) => r._id === action.payload._id
        );
        if (index !== -1) {
          state.reviews[index] = action.payload;
        }
        if (
          state.currentReview &&
          state.currentReview._id === action.payload._id
        ) {
          state.currentReview = action.payload;
        }
      })
      .addCase(unlikeReview.rejected, (state, action) => {
        state.likingReview = false;
        state.error = action.payload || "Failed to unlike review";
      });
  },
});

// Export actions
export const {
  clearError,
  clearSearchResults,
  setCurrentReview,
  clearCurrentReview,
  setReviews,
  clearReviews,
  updateReviewInState,
  resetReviewState,
} = reviewSlice.actions;

// Updated selectors with defensive programming
export const selectReviews = (state: RootState | any): Review[] => {
  return state?.review?.reviews ?? [];
};

export const selectCurrentReview = (state: RootState | any): Review | null => {
  return state?.review?.currentReview ?? null;
};

export const selectFilteredReviews = (state: RootState | any): Review[] => {
  return state?.review?.filteredReviews ?? [];
};

export const selectReviewLoading = (state: RootState | any): boolean => {
  return state?.review?.loading ?? false;
};

export const selectReviewError = (state: RootState | any): string | null => {
  return state?.review?.error ?? null;
};

export const selectSearchResults = (state: RootState | any): Review[] => {
  return state?.review?.searchResults ?? [];
};

export const selectSearchLoading = (state: RootState | any): boolean => {
  return state?.review?.searchLoading ?? false;
};

export const selectPostingReview = (state: RootState | any): boolean => {
  return state?.review?.postingReview ?? false;
};

export const selectUpdatingReview = (state: RootState | any): boolean => {
  return state?.review?.updatingReview ?? false;
};

export const selectDeletingReview = (state: RootState | any): boolean => {
  return state?.review?.deletingReview ?? false;
};

export const selectLikingReview = (state: RootState | any): boolean => {
  return state?.review?.likingReview ?? false;
};

export const selectTotalReviews = (state: RootState | any): number => {
  return state?.review?.totalReviews ?? 0;
};

export const selectAverageRating = (state: RootState | any): number => {
  return state?.review?.averageRating ?? 0;
};

// Additional helper selectors
export const selectReviewState = (state: RootState | any): ReviewState => {
  return state?.review ?? initialState;
};

export const selectIsReviewStateInitialized = (state: RootState | any): boolean => {
  return state?.review !== undefined;
};

// Export types for use in other files
export type { Review, ReviewCreateRequest, ReviewUpdateRequest, ReviewFilters, SearchRequest, ReviewState, RootState };

// Export reducer
export default reviewSlice.reducer;