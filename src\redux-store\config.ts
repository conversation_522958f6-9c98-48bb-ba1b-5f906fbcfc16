/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from "axios";
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";

const isDevelopment = import.meta.env.MODE === "development";

const baseURL = isDevelopment
  ? import.meta.env.VITE_LOCAL_API_URL
  : import.meta.env.VITE_PROD_API_URL || "https://api.patronworks.net/api/v1";

console.log("baseURL", baseURL);

const apiClient: AxiosInstance = axios.create({
  baseURL,
  headers: {
    "Content-Type": "application/json",
  },
});

apiClient.interceptors.request.use(
  (config: AxiosRequestConfig): any => {
    // Use customerAuthToken instead of authToken to match your customer slice
    const token = localStorage.getItem("customerAuthToken");
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }
    return config;
  },
  (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear customer-specific tokens
      localStorage.removeItem("customerAuthToken");
      localStorage.removeItem("customer");
      // Store current location for redirect after login
      const currentPath = window.location.pathname + window.location.search;
      window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
    }
    return Promise.reject(error);
  }
);

export default apiClient;
