import React from 'react';
import type { Restaurant } from '../../pages/Restaurant/types/types'; 
import RestaurantCard from '../RestaurentListbyFilterComponent/RestaurantCard';

interface RestaurantGridProps {
  restaurants: Restaurant[];
  visibleCount: number;
  onShowMore: () => void;
  hasMore: boolean;
  isMobile: boolean;
  userId?: string;
  onFavoriteToggle: (id: string) => void;
  favoriteLoading: boolean;
  title?: string;
}

const RestaurantGrid: React.FC<RestaurantGridProps> = ({
  restaurants,
  visibleCount,
  onShowMore,
  hasMore,
  isMobile,
  userId,
  onFavoriteToggle,
  favoriteLoading,
  title = "All Restaurants"
}) => {
  if (restaurants.length === 0) return null;

  return (
    <div className="mb-8">
      <div className="text-xl font-semibold text-[#070707] mb-6">
        {title}
      </div>
      <div className={`grid ${isMobile ? 'grid-cols-1 sm:grid-cols-2' : 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'} gap-4`}>
        {restaurants.slice(0, visibleCount).map((restaurant) => (
          <div key={restaurant._id} className="w-full">
            <RestaurantCard
              restaurant={restaurant}
              userId={userId}
              onFavoriteToggle={onFavoriteToggle}
              favoriteLoading={favoriteLoading}
            />
          </div>
        ))}
      </div>
      {hasMore && (
        <div className="text-center mt-6">
          <button
            onClick={onShowMore}
            className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
          >
            Show More
          </button>
        </div>
      )}
    </div>
  );
};

export default RestaurantGrid