/* eslint-disable @typescript-eslint/no-empty-object-type */
/* eslint-disable @typescript-eslint/no-explicit-any */

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface ErrorResponse {
  success: false;
  message: string;
  error?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  token: string;
  userId: { _id: string };
  role: string;
  loginDate: string;
  email: string;
  appFee: number;
  name: any;
}

export interface User {
  _id: string;
  userId?: string;
  name: string;
  email: string;
  role: string;
  appFee?: number;
  surChargeThreshold?: number;
  isActive?: boolean;
  createdDate?: string;
}

export interface Product {
  _id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  userId: string;
  isActive?: boolean;
}

export interface Category {
  _id: string;
  name: string;
  description?: string;
  userId: string;
  isActive?: boolean;
}

export interface Order {
  _id: string;
  customerId: string;
  items: OrderItem[];
  total: number;
  status: string;
  createdAt: string;
  userId: string;
}

export interface OrderItem {
  _id: string;
  productId: string;
  quantity: number;
  price: number;
  modifiers?: any[];
}

// Main Customer interface - unified and consistent
export interface Customer {
  _id: string;
  fname: string;
  lname: string;
  Email: string;
  Phone: string; // Always a string, never undefined
  verify: boolean;
  profile_pic?: string;
  // Optional fields
  FirstName?: string;
  LastName?: string;
  currentPassword?: string;
  newPassword?: string;
  address?: string;
  Address1?: {
    street: string;
    city: string;
    state: string;
    zipcode: string;
  };
  CustomerLoyalty?: {
    Points: number;
  };
}

export interface CustomerRegisterRequest {
  Email: string;
  Password: string;
  ConfirmPassword: string;
  referralCode?: string;
  FirstName?: string;
  LastName?: string;
  Phone?: string;
  birthDate?: string;
}

export interface CustomerLoginRequest {
  Email: string;
  Password: string;
}

export interface CustomerLoginResponse {
  Address1: any;
  user(arg0: string, user: any): unknown;
  _id: string;
  FirstName?: string;
  fname?: string;
  LastName?: string;
  lname?: string;
  email?: string;
  Email?: string;
  Phone?: string;
  verify: boolean;
  profile_pic?: string;
  token: string;
  UserName?: string;
}

export interface CustomerUpdateRequest {
  Address1?: {
    street: string;
    city: string;
    state: string;
    zipcode: string;
  };
  contact?: string;
  dateOfBirth?: string;
  fname?: string;
  lname?: string;
  email?: string;
  currentPassword?: string;
  newPassword?: string;
  cardDetails?: any;
  profile_pic?: string;
}

export interface GoogleAuthRequest {
  FirstName: string;
  LastName: string;
  Email: string;
  profile_pic?: string;
  googleId: string;
  referralCode?: string;
  Phone?: string;
  birthDate?: string;
  
}

export interface PasswordResetRequest {
  email: string;
}

export interface OTPConfirmRequest {
  email: string;
  otp: string;
}

export interface UpdatePasswordRequest {
  email: string;
  newPassword: string;
}

export interface AddUserToCustomerRequest {
  customerId: string;
  newUserId: string;
}

// Helper type for safer contact field handling
export type SafeCustomer = Customer & {
  contact: string; // Always a string, never undefined
};

// Utility function to safely get contact field
export const getSafeContact = (customer: Customer): string => {
  return customer.Phone || "";
};

// Base Review Interface
export interface Review {
  _id: string;
  rating: number;
  comment: string;
  userName?: string;
  userId?: string;
  restaurantId: string;
  likes?: number;
  createdAt: string;
  updatedAt: string;
}

// Request Types
export interface PostReviewRequest {
  rating: number;
  comment: string;
  restaurantId: string;
  userName?: string;
  userId?: string;
}

export interface UpdateReviewRequest {
  reviewId: string;
}

export interface DeleteReviewRequest {
  reviewId: string;
}

// Response Types
export interface PostReviewResponse {
  _id: string;
  rating: number;
  comment: string;
  userName?: string;
  userId?: string;
  restaurantId: string;
  likes: number;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateReviewResponse {
  updatedReview: Review;
}

export interface DeleteReviewResponse {
  message: string;
}

// API Error Response
export interface ApiErrorResponse {
  message: string;
  error?: string;
  statusCode?: number;
}

// Redux State Types
export interface ReviewState {
  reviews: Review[];
  loading: boolean;
  error: string | null;
  postingReview: boolean;
  likingReview: boolean;
  deletingReview: boolean;
}

export interface DeleteReviewThunkResponse {
  reviewId: string;
  message: string;
}

// Form Data Types (for frontend forms)
export interface ReviewFormData {
  rating: number;
  review: string;
  profession: string;
}

export interface ReviewFormErrors {
  rating?: string;
  review?: string;
  profession?: string;
  general?: string;
}

// Component Props Types
export interface ReviewComponentProps {
  review: Review;
  onLike?: (reviewId: string) => void;
  onDelete?: (reviewId: string) => void;
  isLiking?: boolean;
  isDeleting?: boolean;
  showActions?: boolean;
}

export interface ReviewListProps {
  reviews: Review[];
  loading?: boolean;
  error?: string | null;
  onLike?: (reviewId: string) => void;
  onDelete?: (reviewId: string) => void;
}

export interface ReviewFormProps {
  onSubmit: (data: PostReviewRequest) => void;
  loading?: boolean;
  error?: string | null;
  initialData?: Partial<ReviewFormData>;
}

// Validation Types
export interface ReviewValidation {
  isValid: boolean;
  errors: ReviewFormErrors;
}

// Review Rating Type
export type ReviewRating = 1 | 2 | 3 | 4 | 5;

// Action Types for Redux
export interface ReviewAction {
  type: string;
  payload?: any;
}

export interface ReviewAsyncThunkConfig {
  state: {
    reviews: ReviewState;
  };
  rejectValue: string;
}

// Custom Hook Return Types
export interface UseReviewsHook {
  reviews: Review[];
  loading: boolean;
  error: string | null;
  postReview: (data: PostReviewRequest) => Promise<void>;
  likeReview: (reviewId: string) => Promise<void>;
  deleteReview: (reviewId: string) => Promise<void>;
  clearError: () => void;
}

// Utility Types
export type ReviewId = string;
export type CustomerId = string;
export type ProductId = string;

// Partial types for updates
export type PartialReview = Partial<Review>;
export type ReviewUpdate = Pick<Review, "_id"> & Partial<Omit<Review, "_id">>;

// Union Types
export type ReviewApiOperation = "post" | "like" | "delete";
export type ReviewSortField = "rating" | "likes" | "createdAt" | "updatedAt";
export type ReviewSortOrder = "asc" | "desc";
export type PostReviewThunkResponse = PostReviewResponse;
export type LikeReviewThunkResponse = Review;
