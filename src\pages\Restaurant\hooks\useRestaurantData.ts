// hooks/useRestaurantData.ts
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import {
  getDevicesPatronpal,
  selectDevices,
  selectDeviceLoading,
  selectDeviceError,
} from "../../../redux-store/slices/deviceSlice";
import type { Restaurant } from "../types/types";

export const useRestaurantData = () => {
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  const devices = useSelector(selectDevices);
  const loading = useSelector(selectDeviceLoading);
  const error = useSelector(selectDeviceError);

  const serviceType = searchParams.get("type") || "all";
  const addressFilter = searchParams.get("address");

  useEffect(() => {
    // Only dispatch if we don't have devices data yet
    if (!devices || devices.length === 0) {
      dispatch(getDevicesPatronpal({}) as any);
    }
  }, [dispatch]);

  const transformDeviceToRestaurant = (device: any): Restaurant => ({
    id: device.id ?? device._id,
    _id: device._id,
    name: device.name,
    businessType: device.businessType,
    image: device.image,
    Line1: device.Line1,
    Line2: device.Line2,
    City: device.City,
    State: device.State,
    Phoneno: device.Phoneno,
    PostalCode: device.PostalCode,
    Country: device.Country,
    active: !!device.active,
    userId: device.userId,
    delivery: device.delivery,
    deliveryStartTime: device.deliveryStartTime,
    deliveryEndTime: device.deliveryEndTime,
    ChargesperKm: device.ChargesperKm,
    ChargesFreeKm: device.ChargesFreeKm,
    pickupStartTime: device.pickupStartTime,
    pickupEndTime: device.pickupEndTime,
    reviews: device.reviews || [],
    favorites: device.favorites || [],
  });

  const { filteredDevices, restaurants } = useMemo(() => {
    const transformedRestaurants =
      devices?.map(transformDeviceToRestaurant) || [];
    const activeRestaurants = transformedRestaurants.filter(
      (restaurant) => restaurant.active
    );

    let filtered = activeRestaurants;

    // Address filtering
    if (addressFilter) {
      filtered = filtered.filter((restaurant) => {
        const filterParts = addressFilter
          .split(/[,\s]+/)
          .map((part) => part.trim().toLowerCase())
          .filter(Boolean);
        const addressString = [
          restaurant.Line1,
          restaurant.Line2,
          restaurant.City,
          restaurant.State,
          restaurant.PostalCode,
          restaurant.Country,
        ]
          .filter(Boolean)
          .map((field) => String(field).toLowerCase())
          .join(" ");
        return filterParts.every((part) => addressString.includes(part));
      });
    }

    // Service type filtering
    switch (serviceType.toLowerCase()) {
      case "delivery":
        filtered = filtered.filter(
          (r) => r.delivery === true || r.delivery === "true"
        );
        break;
      case "pickup":
        filtered = filtered.filter(
          (r) => r.delivery === false || r.delivery === "false"
        );
        break;
      default:
        filtered = filtered.filter(
          (r) =>
            r.delivery === true ||
            r.delivery === "true" ||
            r.delivery === false ||
            r.delivery === "false"
        );
    }

    const filteredDevices = filtered
      .map((restaurant) =>
        devices?.find((device) => device._id === restaurant._id)
      )
      .filter(Boolean);

    return { filteredDevices, restaurants: filtered };
  }, [devices, serviceType, addressFilter]);

  return {
    devices,
    loading,
    error,
    restaurants,
    filteredDevices,
    serviceType,
    addressFilter,
  };
};
