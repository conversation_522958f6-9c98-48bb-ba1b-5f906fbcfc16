import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectIsAuthenticated, selectCurrentCustomer, selectAuthInitialized } from '../redux-store/slices/customerSlice';

import type { ReactNode } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
  requireUserId?: boolean;
}

const ProtectedRoute = ({ children, requireUserId = false }: ProtectedRouteProps) => {
  const location = useLocation();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const currentCustomer = useSelector(selectCurrentCustomer);
  const authInitialized = useSelector(selectAuthInitialized);
  
  // Show loading while auth is being initialized
  if (!authInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }
  
  // Check if user is authenticated
  if (!isAuthenticated || !currentCustomer) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
  
  // Additional check for routes that require userId parameter
  if (requireUserId) {
    const urlParams = window.location.pathname.split('/');
    const userIdFromUrl = urlParams.find((_param, index) => 
      urlParams[index - 1] === 'userid' || 
      (urlParams.includes('product') && index === urlParams.length - 2)
    );
    
    // Verify the userId in URL matches the current user
    if (userIdFromUrl && userIdFromUrl !== currentCustomer._id) {
      return <Navigate to="/login" state={{ from: location }} replace />;
    }
  }
  
  return children;
};

export default ProtectedRoute;