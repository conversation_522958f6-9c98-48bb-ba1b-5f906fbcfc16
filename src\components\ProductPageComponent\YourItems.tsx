/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import { Plus, Minus, ChevronDown, ChevronUp } from "lucide-react";
import { Link } from "react-router-dom";
import ModifierModal from "../Modal/ModifierModal";
import { useAppSelector, useAppDispatch } from "../../redux-store/hooks";
import {
    selectCartItems,
    selectCartTotal,
    selectTotalDeliveryCharges,
    selectTotalWithDelivery,
    updateItemQuantity,
    type CartItem
} from "../../redux-store/slices/cartSlice";

interface YourItemsProps {
    deliveryMethod: string;
    setDeliveryMethod: (method: string) => void;
}

const YourItems: React.FC<YourItemsProps> = ({
    deliveryMethod,
    setDeliveryMethod
}) => {
    const dispatch = useAppDispatch();
    const cartItems = useAppSelector(selectCartItems);
    const totalPrice = useAppSelector(selectCartTotal);
    // const restaurantDeliveryCharges = useAppSelector(selectRestaurantDeliveryCharges);
    const totalDeliveryCharges = useAppSelector(selectTotalDeliveryCharges);
    const totalWithDelivery = useAppSelector(selectTotalWithDelivery);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState<CartItem | null>(null);
    const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

    const closeModal = () => {
        setIsModalOpen(false);
        setSelectedItem(null);
    };

    // Toggle delivery method
    const toggleDeliveryMethod = (method: string) => {
        setDeliveryMethod(method);
    };

    // Update cart item quantity using Redux
    const handleUpdateQuantity = (id: string, action: "increase" | "decrease") => {
        dispatch(updateItemQuantity({ id, action }));
    };

    // Toggle expanded state for an item
    const toggleExpanded = (itemId: string) => {
        setExpandedItems(prev => ({
            ...prev,
            [itemId]: !prev[itemId]
        }));
    };

    // Group cart items by restaurant
    const groupedItems: Record<string, CartItem[]> = {};
    cartItems.forEach(item => {
        // Use restaurant name from the restaurant object if available
        const restaurantName = item.restaurant?.name || 'unknown';
        const restaurantId = item.restaurant?.id || item.restaurantId || 'unknown';

        const key = `${restaurantId}-${restaurantName}`;
        if (!groupedItems[key]) {
            groupedItems[key] = [];
        }
        groupedItems[key].push(item);
    });

    console.log('groupedItems',groupedItems)

    // Format modifier price
    const formatModifierPrice = (price: number) => {
        return price > 0 ? `+$${price.toFixed(2)}` : 'N/A';
    };

    const calculateItemTotalPrice = (item: CartItem) => {
        let totalPrice = item.price;

        if (item.modifiers && Object.keys(item.modifiers).length > 0) {
            Object.values(item.modifiers).forEach((modifierData: any) => {
                if (modifierData.price > 0) {
                    totalPrice += modifierData.price;
                }
            });
        }

        return totalPrice;
    };

    // Calculate subtotal for a restaurant
    // const calculateRestaurantSubtotal = (items: CartItem[]) => {
    //     return items.reduce((total, item) => {
    //         return total + (calculateItemTotalPrice(item) * item.quantity);
    //     }, 0);
    // };

    return (
        <>
            {/* Delivery/Pickup toggle */}
            <div className="bg-primary text-white rounded-full p-1 mb-6 flex">
                <button
                    onClick={() => toggleDeliveryMethod("Delivery")}
                    className={`flex-1 py-2 px-4 rounded-full ${deliveryMethod === "Delivery" ? "bg-white text-black" : ""}`}
                >
                    Delivery
                </button>
                <button
                    onClick={() => toggleDeliveryMethod("Pickup")}
                    className={`flex-1 py-2 px-4 rounded-full ${deliveryMethod === "Pickup" ? "bg-white text-black" : ""}`}
                >
                    Pickup
                </button>
            </div>

            {/* Cart items */}
            <h2 className="text-xl font-bold mb-4">Your Items</h2>

            <div className="mb-6 space-y-4 overflow-y-auto h-[300px] pr-2">
                {Object.entries(groupedItems).map(([restaurantKey, items]) => {
                    const [restaurantId, restaurantName] = restaurantKey.split('-');
                    const firstItem = items[0];
                    const restaurantImage = firstItem.restaurant?.image || firstItem.image;
                    // const restaurantSubtotal = calculateRestaurantSubtotal(items);
                    // const deliveryInfo = restaurantDeliveryCharges[restaurantId];

                    return (
                        <div key={restaurantKey} className="mb-6 border border-gray-200 rounded-lg p-4">
                            {/* Restaurant Header */}
                            <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-100">
                                {restaurantImage && (
                                    <img
                                        src={restaurantImage !== 'undefined' ? restaurantImage : 'https://patronpal.com/assets/aaa.jpeg'}
                                        alt={restaurantId}
                                        className="w-8 h-8 rounded-full object-cover"
                                    />
                                )}
                                <h3 className="font-semibold text-gray-700">{restaurantName}</h3>
                            </div>

                            {/* Restaurant Items */}
                            {items.map((item) => (
                                <div key={item._id} className="flex flex-col border-b border-gray-100 pb-2 mb-2 relative">
                                    <div className="flex items-center gap-3 justify-between">
                                        <div className="flex items-center gap-2 cursor-pointer w-full">
                                            <img
                                                src={item?.image !== 'undefined' ? item?.image || 'https://patronpal.com/assets/aaa.jpeg' : 'https://patronpal.com/assets/aaa.jpeg'}
                                                alt={item?.name}
                                                className="w-16 h-16 rounded-md object-cover"
                                            />
                                            <div className="flex-1 w-full h-full space-y-3">
                                                <div className="flex justify-between items-baseline">
                                                    <h4 className="font-semibold text-base w-full">{item?.name}</h4>
                                                    {item.modifiers && Object.keys(item.modifiers).length > 0 && (
                                                        <button
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                toggleExpanded(item._id);
                                                            }}
                                                            className="ml-1 text-gray-700 cursor-pointer"
                                                        >
                                                            {expandedItems[item._id] ?
                                                                <ChevronUp size={22} /> :
                                                                <ChevronDown size={22} />
                                                            }
                                                        </button>
                                                    )}
                                                </div>
                                                <div className="flex justify-between items-baseline">
                                                    {/* main price with modifiers */}
                                                    <p className="text-primary">$ {calculateItemTotalPrice(item).toFixed(2)}</p>

                                                    <div className="flex items-center gap-2">
                                                        <button
                                                            onClick={() => handleUpdateQuantity(item._id, "decrease")}
                                                            className="rounded-full w-8 h-8 flex items-center justify-center bg-gray-200"
                                                        >
                                                            <Minus size={16} />
                                                        </button>
                                                        <span className="w-4 text-center">{item.quantity}</span>
                                                        <button
                                                            onClick={() => handleUpdateQuantity(item._id, "increase")}
                                                            className="rounded-full w-8 h-8 flex items-center justify-center bg-gray-800 text-white"
                                                        >
                                                            <Plus size={16} />
                                                        </button>
                                                    </div>
                                                </div>

                                                {item.isFree && (
                                                    <p className="text-green-500 text-sm">1x Free</p>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Expanded modifiers section */}
                                    {expandedItems[item._id] && item.modifiers && Object.keys(item.modifiers).length > 0 && (
                                        <div className="px-2 py-2 border rounded-b-2xl border-gray-200 overflow-y-auto absolute z-10 h-[100] w-full right-0 top-[80px] bg-white">
                                            {Object.entries(item.modifiers).map(([modifierName, modifierData]: [string, any]) => (
                                                modifierData.price > 0 && (
                                                    <div key={modifierName} className="flex justify-between text-sm py-1 font-semibold">
                                                        <span className="text-gray-600">{modifierName}: {modifierData.name}</span>
                                                        <span className="text-gray-700 font-medium">
                                                            {formatModifierPrice(modifierData.price)}
                                                        </span>
                                                    </div>
                                                )
                                            ))}

                                            {/* Show note if available */}
                                            {item.note && (
                                                <div className="text-sm py-1 text-gray-600 italic">
                                                    <span className="font-semibold">
                                                        Note:
                                                    </span> {item.note}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            ))}

                            {/* Restaurant Summary */}
                            {/* <div className="mt-3 pt-2 border-t border-gray-100">
                                <div className="flex justify-between text-sm mb-1">
                                    <span className="text-gray-600">Subtotal</span>
                                    <span className="font-medium">$ {restaurantSubtotal.toFixed(2)}</span>
                                </div>
                                
                                {deliveryMethod === "Delivery" && deliveryInfo && (
                                    <div className="flex justify-between text-sm mb-1">
                                        <span className="text-gray-600">Delivery Fee</span>
                                        <span className="font-medium">
                                            {deliveryInfo.deliveryCharge > 0 
                                                ? `$ ${deliveryInfo.deliveryCharge.toFixed(2)}`
                                                : 'Free'
                                            }
                                        </span>
                                    </div>
                                )}
                                
                                <div className="flex justify-between text-sm font-semibold text-primary">
                                    <span>Restaurant Total</span>
                                    <span>
                                        $ {(restaurantSubtotal + (deliveryMethod === "Delivery" && deliveryInfo ? deliveryInfo.deliveryCharge : 0)).toFixed(2)}
                                    </span>
                                </div>
                            </div> */}
                        </div>
                    );
                })}

                {cartItems.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                        Your cart is empty
                    </div>
                )}
            </div>

            {/* Total Summary */}
            <div className="border-t border-dashed mt-4 border-gray-200 pt-4 mb-6">
                <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="font-medium">$ {totalPrice.toFixed(2)}</span>
                </div>
                
                {deliveryMethod === "Delivery" && totalDeliveryCharges > 0 && (
                    <div className="flex justify-between mb-2">
                        <span className="text-gray-600">Total Delivery Fees</span>
                        <span className="font-medium">$ {totalDeliveryCharges.toFixed(2)}</span>
                    </div>
                )}
                
                <div className="flex justify-between mb-3 text-lg">
                    <span className="font-bold text-gray-800">Total</span>
                    <span className="font-bold text-primary">
                        $ {deliveryMethod === "Delivery" ? totalWithDelivery.toFixed(2) : totalPrice.toFixed(2)}
                    </span>
                </div>
                
                <button className="text-blue-500 text-sm">See Summary</button>
            </div>

            {/* Checkout button */}
            <Link 
                to={`/checkout/?type=${deliveryMethod}`} 
                className="w-full cursor-pointer text-center bg-primary text-white px-4 py-3 rounded-xl font-semibold"
            >
                Proceed to Payment
            </Link>

            {/* Render the modal only if we have a selected item */}
            {selectedItem && (
                <ModifierModal
                    isOpen={isModalOpen}
                    onClose={closeModal}
                    productId={selectedItem?.id}
                    userId={selectedItem?.userId}
                    productName={selectedItem?.name}
                    basePrice={selectedItem?.price}
                    onAddToOrder={(_modifiers) => {
                        closeModal();
                    }}
                />
            )}
        </>
    );
};

export default YourItems;