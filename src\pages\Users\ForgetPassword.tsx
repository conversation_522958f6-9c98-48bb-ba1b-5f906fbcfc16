
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import type { AppDispatch } from '../../redux-store/store'; // Adjust path as needed
import {
  sendPasswordResetOTP,
  confirmPasswordResetOTP,
  clearError,
  selectCustomerLoading,
  selectCustomerError,
} from '../../redux-store/slices/customerSlice'; // Adjust path as needed

// Define RootState interface for TypeScript

const ForgetPassword: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  
  // Redux selectors
  const loading = useSelector(selectCustomerLoading);
  const error = useSelector(selectCustomerError);
  
  // Local state
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState(['', '', '', '']); // Changed to 4 digits
  const [emailError, setEmailError] = useState('');
  const [otpError, setOtpError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Clear errors when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Clear local errors when step changes
  useEffect(() => {
    setEmailError('');
    setOtpError('');
    dispatch(clearError());
  }, [step, dispatch]);

  // Handle Redux errors
  useEffect(() => {
    if (error) {
      if (step === 'email') {
        setEmailError(error);
      } else {
        setOtpError(error);
      }
    }
  }, [error, step]);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    // Clear previous errors
    setEmailError('');
    dispatch(clearError());
    
    // Validation
    if (!email.trim()) {
      setEmailError('Email is required');
      return;
    }
    
    if (!validateEmail(email.trim())) {
      toast.error('Please enter a valid email address');
      setEmailError('Please enter a valid email address');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const resultAction = await dispatch(sendPasswordResetOTP({ 
        email: email.trim() 
      }));
      
      if (resultAction.type.endsWith('/fulfilled')) {
        // Success - move to OTP step
        setStep('otp');
        toast.success('OTP has been sent to your email!');
        setOtp(['', '', '', '']); // Reset OTP fields to 4 digits
      } else if (resultAction.type.endsWith('/rejected')) {
        // Handle specific error cases
        const errorMessage = resultAction.payload as string;
        
        if (errorMessage?.toLowerCase().includes('not found') || 
            errorMessage?.toLowerCase().includes('not registered') ||
            errorMessage?.toLowerCase().includes('email not registered')) {
          setEmailError('This email address is not registered with us');
          toast.error('This email address is not registered with us');
        } else {
          setEmailError(errorMessage || 'Failed to send OTP');
          toast.error(errorMessage || 'Failed to send OTP');
        }
      }
    } catch (err) {
      console.error('Email submission error:', err);
      setEmailError('Something went wrong. Please try again.');
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = e.target.value;
    
    // Only allow numbers
    if (value && !/^\d$/.test(value)) return;
    if (value.length > 1) return;
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    // Auto-focus next input (changed condition to index < 3 for 4 digits)
    if (value && index < 3) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      nextInput?.focus();
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      prevInput?.focus();
    }
  };

  const handleOtpSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    // Clear previous errors
    setOtpError('');
    dispatch(clearError());
    
    const otpValue = otp.join('');
    console.log(otpValue, "This is OTP Value");
    if (otpValue.length !== 4) { // Changed from 8 to 4
      toast.error('Please enter all 4 digits')
      setOtpError('Please enter all 4 digits');
      return;
    }

    if (!/^\d{4}$/.test(otpValue)) { // Changed from 8 to 4
      setOtpError('OTP must contain only numbers');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const resultAction = await dispatch(confirmPasswordResetOTP({ 
        email: email.trim(), 
        otp: otpValue 
      }));
      
      if (resultAction.type.endsWith('/fulfilled')) {
        // OTP verified successfully - navigate immediately to reset password page
        toast.success('OTP Verified! Redirecting to reset password page...');
        
        // Navigate immediately to reset password page with email parameter
        navigate(`/reset-password?email=${encodeURIComponent(email)}`);
        
      } else if (resultAction.type.endsWith('/rejected')) {
        const errorMessage = resultAction.payload as string;
        
        if (errorMessage?.toLowerCase().includes('invalid') || 
            errorMessage?.toLowerCase().includes('incorrect') ||
            errorMessage?.toLowerCase().includes('expired')) {
          setOtpError('Invalid or expired OTP. Please try again.');
          toast.error('Invalid or expired OTP. Please try again.');
        } else {
          setOtpError(errorMessage || 'Failed to verify OTP');
          toast.error(errorMessage || 'Failed to verify OTP');
        }
      }
    } catch (err) {
      console.error('OTP verification error:', err);
      setOtpError('Something went wrong. Please try again.');
      toast.error('An error occurred while verifying OTP. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendOtp = async () => {
    setOtp(['', '', '', '']); // Changed to 4 digits
    setOtpError('');
    dispatch(clearError());
    
    setIsSubmitting(true);
    
    try {
      const resultAction = await dispatch(sendPasswordResetOTP({ 
        email: email.trim() 
      }));
      
      if (resultAction.type.endsWith('/fulfilled')) {
        toast.success('New OTP sent to your email!');
      } else {
        setOtpError('Failed to resend OTP. Please try again.');
        toast.error('Failed to resend OTP. Please try again.');
      }
    } catch (err) {
      console.error('Resend OTP error:', err);
      setOtpError('Failed to resend OTP. Please try again.');
      toast.error('Failed to resend OTP. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const goBackToEmail = () => {
    setStep('email');
    setOtp(['', '', '', '']); // Changed to 4 digits
    setOtpError('');
    setEmailError('');
    dispatch(clearError());
  };

  const goBackToSignIn = () => {
    navigate('/signin'); // Adjust route as needed
  };

  const isLoading = loading || isSubmitting;

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-lg bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        {/* Logo */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">
            Patron<span className="text-orange-500">Pal</span>
          </h1>
        </div>

        {step === 'email' ? (
          /* Email Step */
          <div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Forgot Password?
            </h2>
            <p className="text-gray-600 mb-8">
              Enter your email address and we'll send you a verification code to reset your password.
            </p>

            <form onSubmit={handleEmailSubmit}>
              <div className="mb-6">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors ${
                    emailError ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                  autoComplete="email"
                  autoFocus
                />
                {emailError && (
                  <p className="mt-2 text-sm text-red-600 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {emailError}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={isLoading || !email.trim()}
                className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Sending Code...
                  </>
                ) : (
                  'Send Verification Code'
                )}
              </button>
            </form>

            <div className="mt-6 text-center">
              <button
                onClick={goBackToSignIn}
                className="text-orange-500 hover:text-orange-600 font-medium transition-colors"
                disabled={isLoading}
              >
                ← Back to Sign In
              </button>
            </div>
          </div>
        ) : (
          /* OTP Step */
          <div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Enter Verification Code
            </h2>
            <p className="text-gray-600 mb-8">
              We've sent a 4-digit verification code to{' '}
              <span className="font-medium text-gray-900">{email}</span>
            </p>

            <form onSubmit={handleOtpSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Verification Code *
                </label>
                <div className="flex gap-3 justify-center">
                  {otp.map((digit, index) => (
                    <input
                      key={index}
                      id={`otp-${index}`}
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      value={digit}
                      onChange={(e) => handleOtpChange(e, index)}
                      onKeyDown={(e) => handleOtpKeyDown(index, e)}
                      maxLength={1}
                      className={`w-12 h-12 text-center text-lg font-medium border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors ${
                        otpError ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                      disabled={isLoading}
                      autoComplete="off"
                    />
                  ))}
                </div>
                {otpError && (
                  <p className="mt-3 text-sm text-red-600 text-center flex items-center justify-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {otpError}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={isLoading || otp.join('').length !== 4} // Changed from 8 to 4
                className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Verifying Code...
                  </>
                ) : (
                  'Verify Code'
                )}
              </button>
            </form>

            <div className="mt-4 text-center">
              <button
                onClick={handleResendOtp}
                className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
                disabled={isLoading}
              >
                Didn't receive the code?{' '}
                <span className="text-orange-500 hover:text-orange-600 font-medium">
                  Resend Code
                </span>
              </button>
            </div>

            <div className="mt-6 text-center">
              <button
                onClick={goBackToEmail}
                className="text-orange-500 hover:text-orange-600 font-medium transition-colors"
                disabled={isLoading}
              >
                ← Back to Email
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ForgetPassword;