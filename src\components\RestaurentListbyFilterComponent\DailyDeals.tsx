import React, { useRef } from 'react';
import { ChevronRight } from 'lucide-react';
import type { CuisineCategory } from '../../pages/Restaurant/types/types'; 

interface DailyDealsProps {
  businessTypeCategories: CuisineCategory[];
  onBusinessTypeChange: (type: string) => void;
  isMobile: boolean;
}

const DailyDeals: React.FC<DailyDealsProps> = ({ 
  businessTypeCategories, 
  onBusinessTypeChange, 
  isMobile 
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  const scrollSection = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = direction === 'right' ? 300 : -300;
      scrollRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  if (businessTypeCategories.length === 0) return null;

  return (
    <div className="mb-8">
      <div className="text-xl font-semibold mb-4">Your Daily Deals</div>
      <div className="relative">
        <div
          ref={scrollRef}
          className="flex overflow-x-auto space-x-4 pb-2 scrollbar-hide"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {businessTypeCategories.map((category) => (
            <div 
              key={category.categoryId} 
              className={isMobile ? "min-w-[80%]" : "min-w-[33%] flex-shrink-0"}
            >
              <div 
                className="relative cursor-pointer" 
                onClick={() => onBusinessTypeChange(category.name.toLowerCase())}
              >
                <img
                  src={category?.image || 'https://patronpal.com/assets/beverage.jpeg'}
                  alt={category.name}
                  className="w-full h-20 object-cover rounded-lg"
                />
                <div className="absolute bottom-2 left-2 bg-black bg-opacity-60 text-white text-sm px-2 py-1 rounded">
                  {category.name}
                </div>
              </div>
            </div>
          ))}
        </div>
        <button
          className="absolute -right-2.5 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-sm shadow-gray-400 hover:bg-gray-100 z-10"
          onClick={() => scrollSection('right')}
        >
          <ChevronRight className="w-5 h-5 text-primary" />
        </button>
      </div>
    </div>
  );
};

export default DailyDeals;