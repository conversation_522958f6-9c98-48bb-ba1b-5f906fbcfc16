import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import { logout } from "./customerSlice"; // Import the logout action

// Types
export type DeliveryOption = 'Delivery' | 'Pickup';

export interface AddressData {
  Line1: string;
  Line2?: string;
  City: string;
  State: string;
  PostalCode: string;
  Country: string;
}

// State interface
interface AddressState {
  formattedAddress: string;
  fullAddressData: AddressData | null;
  deliveryOption: DeliveryOption;
}

// Initial state
const initialState: AddressState = {
  formattedAddress: "",
  fullAddressData: null,
  deliveryOption: 'Delivery',
};

// Address slice
const addressSlice = createSlice({
  name: "address",
  initialState,
  reducers: {
    setFormattedAddress: (state, action: PayloadAction<string>) => {
      state.formattedAddress = action.payload;
    },
    setFullAddressData: (state, action: PayloadAction<AddressData | null>) => {
      state.fullAddressData = action.payload;
      
      // Also update the formatted address if full data is provided
      if (action.payload) {
        const formattedAddress = [
          action.payload.City,
          action.payload.State,
          action.payload.Country
        ].filter(Boolean).join(', ');
        
        state.formattedAddress = formattedAddress;
      }
      
      // Save to localStorage for persistence
      if (action.payload) {
        localStorage.setItem('lastSelectedAddress', JSON.stringify(action.payload));
      } else {
        localStorage.removeItem('lastSelectedAddress');
      }
    },
    setDeliveryOption: (state, action: PayloadAction<DeliveryOption>) => {
      state.deliveryOption = action.payload;
      // Save to localStorage for persistence
      localStorage.setItem('deliveryOption', action.payload);
    },
    initializeAddressState: (state) => {
      // Load address from localStorage
      const savedAddress = localStorage.getItem('lastSelectedAddress');
      if (savedAddress) {
        try {
          const addressData = JSON.parse(savedAddress) as AddressData;
          state.fullAddressData = addressData;
          
          // Format the address for display
          const formattedAddress = [
            addressData.City,
            addressData.State,
            addressData.Country
          ].filter(Boolean).join(', ');
          
          state.formattedAddress = formattedAddress;
        } catch (error) {
          console.error('Error parsing saved address:', error);
        }
      }
      
      // Load delivery option from localStorage
      const savedOption = localStorage.getItem('deliveryOption') as DeliveryOption | null;
      if (savedOption && (savedOption === 'Delivery' || savedOption === 'Pickup')) {
        state.deliveryOption = savedOption;
      }
    },
    clearAddressState: (state) => {
      state.formattedAddress = "";
      state.fullAddressData = null;
      state.deliveryOption = 'Delivery';
      localStorage.removeItem('lastSelectedAddress');
      localStorage.removeItem('deliveryOption');
    }
  },
  // Add extraReducers to handle logout
  extraReducers: (builder) => {
    builder.addCase(logout, (state) => {
      // Clear address state when user logs out
      state.formattedAddress = "";
      state.fullAddressData = null;
      state.deliveryOption = 'Delivery';
      localStorage.removeItem('lastSelectedAddress');
      localStorage.removeItem('deliveryOption');
    });
  },
});

// Export actions
export const {
  setFormattedAddress,
  setFullAddressData,
  setDeliveryOption,
  initializeAddressState,
  clearAddressState
} = addressSlice.actions;

// Selectors
export const selectFormattedAddress = (state: { address: AddressState }) => 
  state.address.formattedAddress;
export const selectFullAddressData = (state: { address: AddressState }) => 
  state.address.fullAddressData;
export const selectDeliveryOption = (state: { address: AddressState }) => 
  state.address.deliveryOption;

// Export reducer
export default addressSlice.reducer;



