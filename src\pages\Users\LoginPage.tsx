/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { FcGoogle } from 'react-icons/fc';
import { FaApple } from 'react-icons/fa';  
import { toast } from 'react-toastify';
import type { AppDispatch } from '../../redux-store/store';
import {
  loginCustomer,
  googleLogin,
  appleLogin,
  clearError,
  selectCustomerLoading,
  selectCustomerError,
  selectIsAuthenticated,
  selectAuthInitialized,
  initializeAuth
} from '../../redux-store/slices/customerSlice';
import { Eye, EyeOff } from 'lucide-react';

// Types
interface LoginFormData {
  email: string;
  password: string;
}

interface AppleLoginData {
  Email: string;
  appleId: string;
  customToken: string;
  keyId: string;
  authorizationCode: string;
  firstName?: string;
  lastName?: string;
  name: string;
  services: string;
  clientId: string;
}

// Global state management for social logins
interface SocialLoginState {
  google: {
    loaded: boolean;
    initialized: boolean;
    loading: boolean;
  };
  apple: {
    loaded: boolean;
    initialized: boolean;
    loading: boolean;
  };
}

// Global social login manager
class SocialLoginManager {
  private static instance: SocialLoginManager;
  private state: SocialLoginState = {
    google: { loaded: false, initialized: false, loading: false },
    apple: { loaded: false, initialized: false, loading: false }
  };
  private listeners: Set<() => void> = new Set();

  static getInstance(): SocialLoginManager {
    if (!SocialLoginManager.instance) {
      SocialLoginManager.instance = new SocialLoginManager();
    }
    return SocialLoginManager.instance;
  }

  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notify(): void {
    this.listeners.forEach(listener => listener());
  }

  getState(): SocialLoginState {
    return { ...this.state };
  }

  updateState(provider: 'google' | 'apple', updates: Partial<SocialLoginState['google']>): void {
    this.state[provider] = { ...this.state[provider], ...updates };
    this.notify();
  }

  async loadGoogleScript(config: any, handleResponse: (response: any) => void): Promise<void> {
    if (this.state.google.loading) return;
    
    if (window.google?.accounts?.id) {
      this.initializeGoogle(config, handleResponse);
      return;
    }

    if (this.state.google.loaded) return;

    this.updateState('google', { loading: true });

    try {
      const existingScript = document.querySelector('script[src*="accounts.google.com/gsi/client"]');
      if (existingScript) {
        await this.waitForGoogle();
        this.initializeGoogle(config, handleResponse);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      
      const loadPromise = new Promise<void>((resolve, reject) => {
        script.onload = () => {
          this.updateState('google', { loaded: true, loading: false });
          resolve();
        };
        script.onerror = () => {
          this.updateState('google', { loading: false });
          reject(new Error('Failed to load Google script'));
        };
      });

      document.head.appendChild(script);
      await loadPromise;
      this.initializeGoogle(config, handleResponse);
    } catch (error) {
      console.error('❌ Error loading Google script:', error);
      this.updateState('google', { loading: false });
      throw error;
    }
  }

  private async waitForGoogle(): Promise<void> {
    let attempts = 0;
    while (!window.google?.accounts?.id && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
  }

  private initializeGoogle(config: any, handleResponse: (response: any) => void): void {
    try {
      if (window.google?.accounts?.id && !this.state.google.initialized) {
        window.google.accounts.id.initialize({
          client_id: config.GOOGLE_CLIENT_ID,
          callback: handleResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
          use_fedcm_for_prompt: false,
        });
        this.updateState('google', { initialized: true });
        console.log('✅ Google Sign-In initialized successfully');
      }
    } catch (error) {
      console.error('❌ Error initializing Google Sign-In:', error);
    }
  }

  async loadAppleScript(config: any): Promise<void> {
    if (this.state.apple.loading) return;
    
    if (window.AppleID?.auth) {
      this.initializeApple(config);
      return;
    }

    if (this.state.apple.loaded) return;

    this.updateState('apple', { loading: true });

    try {
      const existingScript = document.querySelector('script[src*="appleid.cdn-apple.com"]');
      if (existingScript) {
        await this.waitForApple();
        this.initializeApple(config);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
      script.async = true;
      script.defer = true;
      
      const loadPromise = new Promise<void>((resolve, reject) => {
        script.onload = () => {
          this.updateState('apple', { loaded: true, loading: false });
          resolve();
        };
        script.onerror = () => {
          this.updateState('apple', { loading: false });
          reject(new Error('Failed to load Apple script'));
        };
      });

      document.head.appendChild(script);
      await loadPromise;
      this.initializeApple(config);
    } catch (error) {
      console.error('❌ Error loading Apple script:', error);
      this.updateState('apple', { loading: false });
      throw error;
    }
  }

  private async waitForApple(): Promise<void> {
    let attempts = 0;
    while (!window.AppleID && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
  }

  private initializeApple(config: any): void {
    try {
      if (window.AppleID?.auth && !this.state.apple.initialized) {
        window.AppleID.auth.init({
          clientId: config.APPLE_CLIENT_ID,
          scope: 'name email',
          redirectURI: config.REDIRECT_URI,
          usePopup: true,
          state: JSON.stringify({
            keyId: config.APPLE_KEY_ID,
            name: config.APPLE_NAME,
            services: config.APPLE_SERVICES,
            timestamp: Date.now()
          })
        });
        this.updateState('apple', { initialized: true });
        console.log('✅ Apple Sign-In initialized successfully');
      }
    } catch (error) {
      console.error('❌ Error initializing Apple Sign-In:', error);
    }
  }
}

// Enhanced validation schema with better patterns
const validationSchema = Yup.object({
  email: Yup.string()
    .email('Please enter a valid email address')
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email format'
    )
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .max(50, 'Password must not exceed 50 characters')
    .required('Password is required'),
});

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();

  // Redux selectors
  const isLoading = useSelector(selectCustomerLoading);
  const error = useSelector(selectCustomerError);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authInitialized = useSelector(selectAuthInitialized);
  
  // Local state
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [socialLoading, setSocialLoading] = useState<{
    google: boolean;
    apple: boolean;
  }>({ google: false, apple: false });
  const [socialState, setSocialState] = useState<SocialLoginState>(() => 
    SocialLoginManager.getInstance().getState()
  );

  // Configuration from environment variables with updated fallbacks
  const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || "************-mc939due2sphq0lgslraliga9h36j8gf.apps.googleusercontent.com";
  const APPLE_CLIENT_ID = import.meta.env.VITE_APPLE_NAME || "patronpalLoginKey";
  const APPLE_KEY_ID = import.meta.env.VITE_APPLE_KEY_ID || "3NP7Y9SS6L";
  const REDIRECT_URI = `${window.location.origin}/auth/apple/callback`;

  const config = {
    GOOGLE_CLIENT_ID,
    APPLE_CLIENT_ID,
    APPLE_NAME: APPLE_CLIENT_ID,
    APPLE_KEY_ID,
    APPLE_SERVICES: import.meta.env.VITE_APPLE_SERVICES || "DeviceCheck,Account & Organizational Data Sharing",
    REDIRECT_URI
  };

  // Initial form values
  const initialValues: LoginFormData = {
    email: '',
    password: '',
  };

  // Get social login manager instance
  const socialManager = SocialLoginManager.getInstance();

  // Utility functions
  const togglePasswordVisibility = useCallback((): void => {
    setShowPassword((prev) => !prev);
  }, []);

  // JWT token decoder utility
  const decodeJWT = useCallback((token: string) => {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('❌ Error decoding JWT:', error);
      throw new Error('Invalid token format');
    }
  }, []);

  // Enhanced Google response handler
  const handleGoogleResponse = useCallback(async (response: any) => {
    if (!response?.credential) {
      console.error('❌ No credential received from Google');
      toast.error('No credential received from Google');
      return;
    }

    try {
      setSocialLoading(prev => ({ ...prev, google: true }));
      console.log('🔄 Processing Google response...');
      
      const userData = decodeJWT(response.credential);
      console.log('✅ Decoded Google user data:', {
        email: userData.email,
        sub: userData.sub,
        name: userData.name
      });

      const result = await dispatch(googleLogin({
        Email: userData.email,
        googleId: userData.sub
      }));

      if (googleLogin.fulfilled.match(result)) {
        toast.success('Successfully signed in with Google!');
      } else if (googleLogin.rejected.match(result)) {
        const errorMessage = result.payload || 'Google sign-in failed';
        console.error('❌ Google login failed:', errorMessage);
        
        if (errorMessage.includes('not found') || errorMessage.includes('userNotFound')) {
          toast.info('Account not found. Please sign up first.');
          navigate('/signup');
        } else {
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      console.error('❌ Google Sign-in error:', error);
      
      let errorMessage = 'An error occurred during Google Sign-in';
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error: Unable to connect to server. Please check your internet connection.';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      toast.error(errorMessage);
    } finally {
      setSocialLoading(prev => ({ ...prev, google: false }));
    }
  }, [decodeJWT, dispatch, navigate]);

  // Enhanced Apple response handler
  const handleAppleResponse = useCallback(async (response: any) => {
    if (!response?.authorization) {
      console.error('❌ No authorization received from Apple');
      toast.error('No authorization received from Apple');
      return;
    }

    try {
      setSocialLoading(prev => ({ ...prev, apple: true }));
      console.log('🔄 Processing Apple response...');

      // Parse state data
      let stateData = {};
      try {
        if (response.authorization.state) {
          stateData = JSON.parse(response.authorization.state);
        }
      } catch (e) {
        console.warn('⚠️ Could not parse Apple state data:', e);
      }

      // Extract Apple ID from token
      let appleId = '';
      if (response.authorization.id_token) {
        try {
          const decodedToken = decodeJWT(response.authorization.id_token);
          appleId = decodedToken.sub || '';
        } catch (error) {
          console.error('❌ Error decoding Apple ID token:', error);
        }
      }

      const appleLoginData: AppleLoginData = {
        Email: response.user?.email || '',
        appleId: appleId,
        customToken: response.authorization.id_token || '',
        keyId: config.APPLE_KEY_ID,
        authorizationCode: response.authorization.code || '',
        firstName: response.user?.name?.firstName || '',
        lastName: response.user?.name?.lastName || '',
        name: config.APPLE_NAME,
        services: config.APPLE_SERVICES,
        clientId: config.APPLE_CLIENT_ID,
        ...stateData
      };

      console.log('✅ Apple login data prepared:', {
        ...appleLoginData,
        customToken: appleLoginData.customToken ? '[TOKEN_PRESENT]' : '[NO_TOKEN]'
      });

      const result = await dispatch(appleLogin(appleLoginData));

      if (appleLogin.fulfilled.match(result)) {
        toast.success('Successfully signed in with Apple!');
      } else if (appleLogin.rejected.match(result)) {
        const errorMessage = result.payload || 'Apple sign-in failed';
        console.error('❌ Apple login failed:', errorMessage);
        
        if (errorMessage.includes('not found') || errorMessage.includes('userNotFound')) {
          toast.info('Account not found. Please sign up first.');
          navigate('/signup');
        } else {
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      console.error('❌ Apple Sign-in error:', error);
      
      let errorMessage = 'An error occurred during Apple Sign-in';
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error: Unable to connect to server. Please check your internet connection.';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      toast.error(errorMessage);
    } finally {
      setSocialLoading(prev => ({ ...prev, apple: false }));
    }
  }, [decodeJWT, dispatch, navigate, config]);

  // Enhanced form submission
  const handleSubmit = useCallback(async (values: LoginFormData, { setSubmitting }: any) => {
    dispatch(clearError());

    try {
      console.log('🔄 Attempting login for:', values.email);
      
      const result = await dispatch(loginCustomer({
        Email: values.email.trim().toLowerCase(),
        Password: values.password
      }));

      if (loginCustomer.fulfilled.match(result)) {
        toast.success('Successfully logged in!');
        console.log('✅ Login successful');
      } else if (loginCustomer.rejected.match(result)) {
        const errorMessage = result.payload || 'Login failed';
        console.error('❌ Login failed:', errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  }, [dispatch]);

  // Enhanced Google Sign-In handler
  const handleGoogleSignIn = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('🔄 Google Sign-In button clicked');

    if (socialLoading.google) {
      return; // Prevent multiple clicks
    }

    // Set loading state
    setSocialLoading(prev => ({ ...prev, google: true }));

    if (window.google?.accounts?.id) {
      try {
        console.log('🔄 Triggering Google Sign-In...');
        window.google.accounts.id.disableAutoSelect();
        window.google.accounts.id.prompt((notification: any) => {
          if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
            console.log('Google Sign-In prompt not displayed');
            setSocialLoading(prev => ({ ...prev, google: false }));
            toast.error('Google Sign-In popup was blocked. Please allow popups and try again.');
          }
        });
      } catch (error) {
        console.error('❌ Error triggering Google Sign-In:', error);
        setSocialLoading(prev => ({ ...prev, google: false }));
        toast.error('Failed to open Google Sign-In. Please try again.');
      }
    } else {
      console.error('❌ Google Sign-In not loaded');
      setSocialLoading(prev => ({ ...prev, google: false }));
      toast.error('Google Sign-In is not available. Please refresh the page and try again.');
    }
  }, [socialLoading.google]);

  // Enhanced Apple Sign-In handler
  const handleAppleSignIn = useCallback(async () => {
    if (!socialState.apple.initialized) {
      toast.error('Apple Sign-In is still loading. Please wait a moment and try again.');
      return;
    }

    if (socialLoading.apple) {
      return; // Prevent multiple clicks
    }

    if (window.AppleID?.auth) {
      try {
        console.log('🔄 Attempting Apple Sign-In...');
        const response = await window.AppleID.auth.signIn({
          usePopup: true,
          state: JSON.stringify({
            keyId: config.APPLE_KEY_ID,
            name: config.APPLE_NAME,
            services: config.APPLE_SERVICES,
            timestamp: Date.now()
          })
        });
        console.log('✅ Apple Sign-In response received');
        await handleAppleResponse(response);
      } catch (error) {
        console.error('❌ Error with Apple Sign-In:', error);
        
        // Don't show error for user-cancelled actions
        const errorString = error?.toString() || '';
        if (error !== 'popup_closed_by_user' && 
            !errorString.includes('popup_closed_by_user') &&
            !errorString.includes('user_cancelled_authorize')) {
          toast.error('Failed to sign in with Apple. Please try again.');
        }
      }
    } else {
      console.error('❌ Apple Sign-In not loaded');
      toast.error('Apple Sign-In is not available. Please refresh the page and try again.');
    }
  }, [socialState.apple.initialized, socialLoading.apple, config, handleAppleResponse]);

  // Initialize social logins
  const initializeSocialLogins = useCallback(async () => {
    try {
      const promises = [];
      
      if (!socialState.google.loaded && !socialState.google.loading) {
        promises.push(socialManager.loadGoogleScript(config, handleGoogleResponse));
      }
      
      if (!socialState.apple.loaded && !socialState.apple.loading) {
        promises.push(socialManager.loadAppleScript(config));
      }

      if (promises.length > 0) {
        await Promise.allSettled(promises);
      }
    } catch (error) {
      console.error('❌ Error initializing social logins:', error);
    }
  }, [socialState, config, handleGoogleResponse, socialManager]);

  // Effects
  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  useEffect(() => {
    if (authInitialized && isAuthenticated) {
      console.log('✅ User authenticated, redirecting...');
      // Get the redirect URL from location state, URL params, or default to home
      const urlParams = new URLSearchParams(location.search);
      const redirectParam = urlParams.get('redirect');
      const redirectTo = location.state?.from?.pathname || redirectParam || '/';
      navigate(redirectTo, { replace: true });
    }
  }, [isAuthenticated, authInitialized, navigate, location.state, location.search]);

  // Subscribe to social login state changes
  useEffect(() => {
    const unsubscribe = socialManager.subscribe(() => {
      setSocialState(socialManager.getState());
    });

    return unsubscribe;
  }, [socialManager]);

  // Initialize social logins only when needed
  useEffect(() => {
    if (authInitialized && !isAuthenticated) {
      initializeSocialLogins();
    }
  }, [authInitialized, isAuthenticated, initializeSocialLogins]);

  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  // Check if any social login is in progress
  const isSocialLoading = socialLoading.google || socialLoading.apple;

  // Loading state
  if (!authInitialized) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF5C00] mx-auto"></div>
          <p className="mt-4 text-gray-600 text-lg">Initializing...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-50 p-5">
      {/* Social Login Loading Overlay */}
      {isSocialLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-sm w-full mx-4 text-center shadow-2xl">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF5C00] mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {socialLoading.google ? 'Signing in with Google' : 'Signing in with Apple'}
            </h3>
            <p className="text-gray-600 text-sm">
              Please wait while we process your authentication...
            </p>
          </div>
        </div>
      )}

      <div className="w-full max-w-md">
        <div className="text-left mb-8">
          
          <h1 className="text-2xl font-bold text-gray-900">
            Patron<span className='text-[#FF5C00]'>Pal</span>
          </h1>
         
          <h2 className="text-xl font-semibold mt-4 mb-2">Login to your Account</h2>
          <p className="text-gray-600 text-sm">
            Welcome back! Please sign in to continue to your account.
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm" role="alert">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-2">
                <span>{error}</span>
              </div>
            </div>
          </div>
        )}

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          validateOnChange={true}
          validateOnBlur={true}
        >
          {({ isSubmitting, touched, errors }) => (
            <Form noValidate>
              <div className="mb-4">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <Field
                  type="email"
                  id="email"
                  name="email"
                  autoComplete="username email"
                  placeholder="Enter your email address"
                  className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 transition-colors ${
                    touched.email && errors.email
                      ? 'border-red-300 focus:ring-red-500'
                      : 'border-gray-300 focus:ring-[#FF5C00]'
                  }`}
                  disabled={isLoading || isSubmitting || isSocialLoading}
                />
                <ErrorMessage name="email" component="div" className="text-red-600 text-xs mt-1" />
              </div>

              <div className="mb-2 relative">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password *
                </label>
                <Field
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  autoComplete="current-password"
                  placeholder="Enter your password"
                  className={`w-full px-3 py-2 pr-10 border bg-white rounded-md focus:outline-none focus:ring-2 transition-colors ${
                    touched.password && errors.password
                      ? 'border-red-300 focus:ring-red-500'
                      : 'border-gray-300 focus:ring-[#FF5C00]'
                  }`}
                  disabled={isLoading || isSubmitting || isSocialLoading}
                />
                <ErrorMessage name="password" component="div" className="text-red-600 text-xs mt-1" />

                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute right-3 top-[38px] text-gray-500 hover:text-gray-800 focus:outline-none transition-colors"
                  tabIndex={-1}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                  disabled={isSocialLoading}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>

              <div className="flex justify-end mb-6">
                <Link 
                  to="/forget-password" 
                  className="text-sm text-[#FF5C00] hover:text-[#E54E00] transition-colors focus:outline-none focus:underline"
                >
                  Forgot password?
                </Link>
              </div>

              <button
                type="submit"
                disabled={isLoading || isSubmitting || isSocialLoading}
                className="w-full bg-[#FF5C00] text-white py-2 rounded-md hover:bg-[#E54E00] transition-colors focus:outline-none focus:ring-2 focus:ring-[#FF5C00] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {isLoading || isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </span>
                ) : (
                  'Sign in'
                )}
              </button>
            </Form>
          )}
        </Formik>

        <div className="mt-6 text-center">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
            </div>
          </div>
        </div>

        {/* Social Login Buttons */}
        <div className="mt-6 space-y-3">
          <div className="relative">
            <button
              onClick={handleGoogleSignIn}
              disabled={isLoading || socialLoading.google}
              className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5C00] disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
              type="button"
              style={{ position: 'relative', zIndex: 10 }}
            >
              {socialLoading.google ? (
                <svg className="animate-spin w-5 h-5 mr-2 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <FcGoogle className="w-5 h-5 mr-2" />
              )}
              {socialLoading.google ? 'Signing in...' : 'Continue with Google'}
            </button>
          </div>

          <button
            onClick={handleAppleSignIn}
            disabled={isLoading || socialLoading.apple || !socialState.apple || isSocialLoading}
            className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5C00] disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
          >
            <FaApple className="w-5 h-5 mr-2" />
            {!socialState.apple.initialized ? 'Loading Apple...' : 'Continue with Apple ID'}
          </button>
        </div>

       <div className="mt-6 text-center space-y-4">
  <p className="text-sm text-gray-600">
    Don't have an account?{' '}
    <Link 
      to="/signup" 
      className="text-[#FF5C00] hover:text-[#E54E00] font-medium transition-colors focus:outline-none focus:underline"
    >
      Sign up
    </Link> 
    <p className="text-sm text-gray-600 pl-5">OR
  </p>
   <button
    onClick={() => navigate("/")}
    className="text-sm text-gray-600 hover:text-[#FF5C00] transition-colors"
  >
    go to <span className= "text-orange-500">Home</span>
  </button>
  </p> 
  
 
</div>
      </div>
    </div>
  );
};

export default LoginPage;